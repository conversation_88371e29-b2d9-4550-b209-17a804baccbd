/**
 * 权限管理API服务
 * 基于后端Swagger API文档实现
 */

import { defHttp } from '@/utils/http/axios'
import type {
  Permission,
  PermissionCreateRequest,
  PermissionUpdateRequest,
  PermissionQueryParams,
  PermissionListResponse,
  PermissionDetailResponse,
  PermissionOperationResponse,
  PermissionTreeResponse
} from '@/types/permission'
import type { ApiResponse } from '@/types/common'

/**
 * 权限管理API类
 */
export class PermissionApi {
  private static readonly BASE_URL = '/api/permissions'

  /**
   * 分页查询权限列表
   * @param params 查询参数
   * @returns 权限分页列表
   */
  static async getList(params?: PermissionQueryParams): Promise<PermissionListResponse> {
    return defHttp.get({
      url: PermissionApi.BASE_URL,
      params
    })
  }

  /**
   * 查询所有权限
   * @returns 所有权限列表
   */
  static async getAll(): Promise<ApiResponse<Permission[]>> {
    return defHttp.get({
      url: `${PermissionApi.BASE_URL}/all`
    })
  }

  /**
   * 查询权限树
   * @returns 权限树结构
   */
  static async getTree(): Promise<PermissionTreeResponse> {
    return defHttp.get({
      url: `${PermissionApi.BASE_URL}/tree`
    })
  }

  /**
   * 查询根权限
   * @returns 根权限列表
   */
  static async getRoot(): Promise<ApiResponse<Permission[]>> {
    return defHttp.get({
      url: `${PermissionApi.BASE_URL}/root`
    })
  }

  /**
   * 查询系统权限
   * @returns 系统权限列表
   */
  static async getSystem(): Promise<ApiResponse<Permission[]>> {
    return defHttp.get({
      url: `${PermissionApi.BASE_URL}/system`
    })
  }

  /**
   * 根据权限类型查询权限
   * @param permissionType 权限类型
   * @returns 权限列表
   */
  static async getByType(permissionType: number): Promise<ApiResponse<Permission[]>> {
    return defHttp.get({
      url: `${PermissionApi.BASE_URL}/type/${permissionType}`
    })
  }

  /**
   * 根据ID查询权限详情
   * @param id 权限ID
   * @returns 权限详情
   */
  static async getById(id: number): Promise<PermissionDetailResponse> {
    return defHttp.get({
      url: `${PermissionApi.BASE_URL}/${id}`
    })
  }

  /**
   * 根据权限编码查询权限
   * @param permissionCode 权限编码
   * @returns 权限详情
   */
  static async getByCode(permissionCode: string): Promise<PermissionDetailResponse> {
    return defHttp.get({
      url: `${PermissionApi.BASE_URL}/code/${permissionCode}`
    })
  }

  /**
   * 查询子权限
   * @param parentId 父权限ID
   * @returns 子权限列表
   */
  static async getChildren(parentId: number): Promise<ApiResponse<Permission[]>> {
    return defHttp.get({
      url: `${PermissionApi.BASE_URL}/${parentId}/children`
    })
  }

  /**
   * 创建权限
   * @param data 权限创建数据
   * @returns 创建的权限
   */
  static async create(data: PermissionCreateRequest): Promise<PermissionOperationResponse> {
    return defHttp.post({
      url: PermissionApi.BASE_URL,
      data
    })
  }

  /**
   * 更新权限
   * @param id 权限ID
   * @param data 权限更新数据
   * @returns 更新的权限
   */
  static async update(id: number, data: PermissionUpdateRequest): Promise<PermissionOperationResponse> {
    return defHttp.put({
      url: `${PermissionApi.BASE_URL}/${id}`,
      data
    })
  }

  /**
   * 删除权限
   * @param id 权限ID
   * @returns 删除结果
   */
  static async delete(id: number): Promise<ApiResponse<void>> {
    return defHttp.delete({
      url: `${PermissionApi.BASE_URL}/${id}`
    })
  }

  /**
   * 批量删除权限
   * @param ids 权限ID列表
   * @returns 删除结果
   */
  static async batchDelete(ids: number[]): Promise<ApiResponse<void>> {
    return defHttp.delete({
      url: `${PermissionApi.BASE_URL}/batch`,
      data: ids
    })
  }

  /**
   * 检查权限编码是否存在
   * @param permissionCode 权限编码
   * @param excludeId 排除的权限ID
   * @returns 是否存在
   */
  static async checkCode(permissionCode: string, excludeId?: number): Promise<ApiResponse<boolean>> {
    return defHttp.get({
      url: `${PermissionApi.BASE_URL}/check-code`,
      params: { permissionCode, excludeId }
    })
  }

  /**
   * 验证权限编码格式
   * @param permissionCode 权限编码
   * @returns 是否有效
   */
  static async validateCode(permissionCode: string): Promise<ApiResponse<boolean>> {
    return defHttp.get({
      url: `${PermissionApi.BASE_URL}/validate-code`,
      params: { permissionCode }
    })
  }

  /**
   * 统计权限数量
   * @returns 权限总数
   */
  static async count(): Promise<ApiResponse<number>> {
    return defHttp.get({
      url: `${PermissionApi.BASE_URL}/count`
    })
  }

  /**
   * 查询用户权限
   * @param userId 用户ID
   * @returns 用户权限列表
   */
  static async getUserPermissions(userId: number): Promise<ApiResponse<Permission[]>> {
    return defHttp.get({
      url: `${PermissionApi.BASE_URL}/user/${userId}`
    })
  }

  /**
   * 查询用户权限编码
   * @param userId 用户ID
   * @returns 用户权限编码列表
   */
  static async getUserPermissionCodes(userId: number): Promise<ApiResponse<string[]>> {
    return defHttp.get({
      url: `${PermissionApi.BASE_URL}/user/${userId}/codes`
    })
  }

  /**
   * 检查用户是否拥有指定权限
   * @param userId 用户ID
   * @param permissionCode 权限编码
   * @returns 是否拥有权限
   */
  static async checkUserPermission(userId: number, permissionCode: string): Promise<ApiResponse<boolean>> {
    return defHttp.get({
      url: `${PermissionApi.BASE_URL}/user/${userId}/check/${permissionCode}`
    })
  }
}

// 导出默认实例
export default PermissionApi

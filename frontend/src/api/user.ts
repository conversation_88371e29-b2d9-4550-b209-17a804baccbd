/**
 * 用户管理API服务
 * 基于后端Swagger API文档实现
 */

import { defHttp } from '@/utils/http/axios'
import type {
  UserAccount,
  UserCreateRequest,
  UserUpdateRequest,
  UserQueryParams,
  UserListResponse,
  UserDetailResponse,
  UserOperationResponse,
  UserRoleAssignRequest,
  UserPasswordResetRequest,
  UserBatchOperationRequest,
  UserImportData,
  UserExportParams,
  UserStatistics,
  UserActivityLog,
  UserOnlineStatus
} from '@/types/user'
import type { ApiResponse } from '@/types/common'

/**
 * 用户管理API类
 */
export class UserApi {
  private static readonly BASE_URL = '/api/users'

  /**
   * 分页查询用户列表
   * @param params 查询参数
   * @returns 用户列表响应
   */
  static async getUsers(params: UserQueryParams = {}): Promise<UserListResponse> {
    const { page = 1, size = 10, ...rest } = params
    return http.get<UserListResponse>(`${this.BASE_URL}`, {
      params: { page, size, ...rest }
    })
  }

  /**
   * 根据ID查询用户详情
   * @param id 用户ID
   * @returns 用户详情响应
   */
  static async getUserById(id: number): Promise<UserDetailResponse> {
    return http.get<UserDetailResponse>(`${this.BASE_URL}/${id}`)
  }

  /**
   * 根据用户名查询用户
   * @param username 用户名
   * @returns 用户详情响应
   */
  static async getUserByUsername(username: string): Promise<UserDetailResponse> {
    return http.get<UserDetailResponse>(`${this.BASE_URL}/by-username/${username}`)
  }

  /**
   * 创建用户
   * @param data 用户创建数据
   * @returns 用户操作响应
   */
  static async createUser(data: UserCreateRequest): Promise<UserOperationResponse> {
    return http.post<UserOperationResponse>(this.BASE_URL, data)
  }

  /**
   * 更新用户信息
   * @param id 用户ID
   * @param data 用户更新数据
   * @returns 用户操作响应
   */
  static async updateUser(id: number, data: UserUpdateRequest): Promise<UserOperationResponse> {
    return http.put<UserOperationResponse>(`${this.BASE_URL}/${id}`, data)
  }

  /**
   * 删除用户（软删除）
   * @param id 用户ID
   * @returns 删除结果
   */
  static async deleteUser(id: number): Promise<ApiResponse<boolean>> {
    return http.delete<ApiResponse<boolean>>(`${this.BASE_URL}/${id}`)
  }

  /**
   * 检查用户名是否可用
   * @param username 用户名
   * @returns 是否可用
   */
  static async checkUsername(username: string): Promise<ApiResponse<boolean>> {
    return http.get<ApiResponse<boolean>>(`${this.BASE_URL}/check-username`, {
      params: { username }
    })
  }

  /**
   * 批量删除用户
   * @param userIds 用户ID数组
   * @returns 操作结果
   */
  static async batchDeleteUsers(userIds: number[]): Promise<ApiResponse<void>> {
    return http.delete<ApiResponse<void>>(`${this.BASE_URL}/batch`, {
      data: userIds
    })
  }

  /**
   * 启用用户
   * @param id 用户ID
   * @returns 操作结果
   */
  static async enableUser(id: number): Promise<ApiResponse<void>> {
    return http.put<ApiResponse<void>>(`${this.BASE_URL}/${id}/enable`)
  }

  /**
   * 禁用用户
   * @param id 用户ID
   * @returns 操作结果
   */
  static async disableUser(id: number): Promise<ApiResponse<void>> {
    return http.put<ApiResponse<void>>(`${this.BASE_URL}/${id}/disable`)
  }

  /**
   * 锁定用户
   * @param id 用户ID
   * @returns 操作结果
   */
  static async lockUser(id: number): Promise<ApiResponse<void>> {
    return http.put<ApiResponse<void>>(`${this.BASE_URL}/${id}/lock`)
  }

  /**
   * 解锁用户
   * @param id 用户ID
   * @returns 操作结果
   */
  static async unlockUser(id: number): Promise<ApiResponse<void>> {
    return http.put<ApiResponse<void>>(`${this.BASE_URL}/${id}/unlock`)
  }

  /**
   * 重置用户密码
   * @param data 密码重置数据
   * @returns 操作结果
   */
  static async resetPassword(data: UserPasswordResetRequest): Promise<ApiResponse<void>> {
    return http.post<ApiResponse<void>>(`${this.BASE_URL}/${data.userId}/reset-password`, data)
  }

  /**
   * 分配用户角色
   * @param data 角色分配数据
   * @returns 操作结果
   */
  static async assignRoles(data: UserRoleAssignRequest): Promise<ApiResponse<void>> {
    return http.post<ApiResponse<void>>(`${this.BASE_URL}/${data.userId}/roles`, data)
  }

  /**
   * 获取用户角色列表
   * @param userId 用户ID
   * @returns 角色列表
   */
  static async getUserRoles(userId: number): Promise<ApiResponse<string[]>> {
    return http.get<ApiResponse<string[]>>(`${this.BASE_URL}/${userId}/roles`)
  }

  /**
   * 获取用户权限列表
   * @param userId 用户ID
   * @returns 权限列表
   */
  static async getUserPermissions(userId: number): Promise<ApiResponse<string[]>> {
    return http.get<ApiResponse<string[]>>(`${this.BASE_URL}/${userId}/permissions`)
  }

  /**
   * 批量操作用户
   * @param data 批量操作数据
   * @returns 操作结果
   */
  static async batchOperation(data: UserBatchOperationRequest): Promise<ApiResponse<void>> {
    return http.post<ApiResponse<void>>(`${this.BASE_URL}/batch-operation`, data)
  }

  /**
   * 导入用户数据
   * @param data 导入数据
   * @returns 导入结果
   */
  static async importUsers(data: UserImportData[]): Promise<ApiResponse<any>> {
    return http.post<ApiResponse<any>>(`${this.BASE_URL}/import`, data)
  }

  /**
   * 导出用户数据
   * @param params 导出参数
   * @returns 导出文件
   */
  static async exportUsers(params: UserExportParams): Promise<Blob> {
    return http.get(`${this.BASE_URL}/export`, {
      params,
      responseType: 'blob'
    })
  }

  /**
   * 获取用户统计信息
   * @returns 统计信息
   */
  static async getUserStatistics(): Promise<ApiResponse<UserStatistics>> {
    return http.get<ApiResponse<UserStatistics>>(`${this.BASE_URL}/statistics`)
  }

  /**
   * 获取用户活动日志
   * @param userId 用户ID
   * @param params 查询参数
   * @returns 活动日志列表
   */
  static async getUserActivityLogs(
    userId: number,
    params: { page?: number; size?: number } = {}
  ): Promise<ApiResponse<UserActivityLog[]>> {
    return http.get<ApiResponse<UserActivityLog[]>>(`${this.BASE_URL}/${userId}/activity-logs`, {
      params
    })
  }

  /**
   * 获取在线用户列表
   * @returns 在线用户列表
   */
  static async getOnlineUsers(): Promise<ApiResponse<UserOnlineStatus[]>> {
    return http.get<ApiResponse<UserOnlineStatus[]>>(`${this.BASE_URL}/online`)
  }

  /**
   * 强制用户下线
   * @param userId 用户ID
   * @returns 操作结果
   */
  static async forceLogout(userId: number): Promise<ApiResponse<void>> {
    return http.post<ApiResponse<void>>(`${this.BASE_URL}/${userId}/force-logout`)
  }

  /**
   * 上传用户头像
   * @param userId 用户ID
   * @param file 头像文件
   * @returns 上传结果
   */
  static async uploadAvatar(userId: number, file: File): Promise<ApiResponse<string>> {
    const formData = new FormData()
    formData.append('file', file)
    return http.post<ApiResponse<string>>(`${this.BASE_URL}/${userId}/avatar`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  }

  /**
   * 获取用户头像
   * @param userId 用户ID
   * @returns 头像URL
   */
  static getUserAvatarUrl(userId: number): string {
    return `${this.BASE_URL}/${userId}/avatar`
  }

  /**
   * 验证用户邮箱
   * @param userId 用户ID
   * @param code 验证码
   * @returns 验证结果
   */
  static async verifyEmail(userId: number, code: string): Promise<ApiResponse<void>> {
    return http.post<ApiResponse<void>>(`${this.BASE_URL}/${userId}/verify-email`, { code })
  }

  /**
   * 验证用户手机号
   * @param userId 用户ID
   * @param code 验证码
   * @returns 验证结果
   */
  static async verifyPhone(userId: number, code: string): Promise<ApiResponse<void>> {
    return http.post<ApiResponse<void>>(`${this.BASE_URL}/${userId}/verify-phone`, { code })
  }

  /**
   * 发送邮箱验证码
   * @param userId 用户ID
   * @returns 发送结果
   */
  static async sendEmailVerificationCode(userId: number): Promise<ApiResponse<void>> {
    return http.post<ApiResponse<void>>(`${this.BASE_URL}/${userId}/send-email-code`)
  }

  /**
   * 发送手机验证码
   * @param userId 用户ID
   * @returns 发送结果
   */
  static async sendPhoneVerificationCode(userId: number): Promise<ApiResponse<void>> {
    return http.post<ApiResponse<void>>(`${this.BASE_URL}/${userId}/send-phone-code`)
  }
}

// 导出默认实例
export default UserApi

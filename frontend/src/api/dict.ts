/**
 * 字典管理API服务
 */

import { defHttp } from '@/utils/http/axios'
import type {
  DictType,
  DictData,
  DictTypeQueryParams,
  DictDataQueryParams,
  DictTypeCreateRequest,
  DictTypeUpdateRequest,
  DictDataCreateRequest,
  DictDataUpdateRequest,
  DictTypeListResponse,
  DictDataListResponse,
  DictResponse
} from '@/types/dict'

// API路径常量
const API_PATHS = {
  // 字典类型相关
  DICT_TYPE_LIST: '/system/dict/type/list',
  DICT_TYPE_CREATE: '/system/dict/type',
  DICT_TYPE_UPDATE: '/system/dict/type',
  DICT_TYPE_DELETE: '/system/dict/type',
  DICT_TYPE_DETAIL: '/system/dict/type',
  DICT_TYPE_EXPORT: '/system/dict/type/export',
  DICT_TYPE_REFRESH: '/system/dict/type/refreshCache',
  
  // 字典数据相关
  DICT_DATA_LIST: '/system/dict/data/list',
  DICT_DATA_CREATE: '/system/dict/data',
  DICT_DATA_UPDATE: '/system/dict/data',
  DICT_DATA_DELETE: '/system/dict/data',
  DICT_DATA_DETAIL: '/system/dict/data',
  DICT_DATA_TYPE: '/system/dict/data/type'
}

/**
 * 字典类型API服务类
 */
export class DictTypeApi {
  /**
   * 获取字典类型列表
   * @param params 查询参数
   * @returns 字典类型列表响应
   */
  static async getList(params?: DictTypeQueryParams): Promise<DictTypeListResponse> {
    return defHttp.get({
      url: API_PATHS.DICT_TYPE_LIST,
      params
    })
  }

  /**
   * 创建字典类型
   * @param data 创建数据
   * @returns 创建响应
   */
  static async create(data: DictTypeCreateRequest): Promise<DictResponse> {
    return defHttp.post({
      url: API_PATHS.DICT_TYPE_CREATE,
      data
    })
  }

  /**
   * 更新字典类型
   * @param id 字典类型ID
   * @param data 更新数据
   * @returns 更新响应
   */
  static async update(id: number, data: DictTypeUpdateRequest): Promise<DictResponse> {
    return defHttp.put({
      url: `${API_PATHS.DICT_TYPE_UPDATE}/${id}`,
      data
    })
  }

  /**
   * 删除字典类型
   * @param id 字典类型ID
   * @returns 删除响应
   */
  static async delete(id: number): Promise<DictResponse> {
    return defHttp.delete({
      url: `${API_PATHS.DICT_TYPE_DELETE}/${id}`
    })
  }

  /**
   * 批量删除字典类型
   * @param ids 字典类型ID数组
   * @returns 删除响应
   */
  static async batchDelete(ids: number[]): Promise<DictResponse> {
    return defHttp.delete({
      url: API_PATHS.DICT_TYPE_DELETE,
      data: { ids }
    })
  }

  /**
   * 获取字典类型详情
   * @param id 字典类型ID
   * @returns 字典类型详情
   */
  static async getDetail(id: number): Promise<DictResponse> {
    return defHttp.get({
      url: `${API_PATHS.DICT_TYPE_DETAIL}/${id}`
    })
  }

  /**
   * 导出字典类型
   * @param params 查询参数
   * @returns 导出文件
   */
  static async export(params?: DictTypeQueryParams): Promise<Blob> {
    return defHttp.get({
      url: API_PATHS.DICT_TYPE_EXPORT,
      params,
      responseType: 'blob'
    })
  }

  /**
   * 刷新字典缓存
   * @returns 刷新响应
   */
  static async refreshCache(): Promise<DictResponse> {
    return defHttp.delete({
      url: API_PATHS.DICT_TYPE_REFRESH
    })
  }

  /**
   * 获取所有字典类型选项（用于下拉选择）
   * @returns 字典类型选项列表
   */
  static async getOptions(): Promise<Array<{ label: string; value: string }>> {
    const response = await this.getList({ pageSize: 1000 })
    return response.data.records.map(item => ({
      label: item.dictName,
      value: item.dictType
    }))
  }
}

/**
 * 字典数据API服务类
 */
export class DictDataApi {
  /**
   * 获取字典数据列表
   * @param params 查询参数
   * @returns 字典数据列表响应
   */
  static async getList(params: DictDataQueryParams): Promise<DictDataListResponse> {
    return defHttp.get({
      url: API_PATHS.DICT_DATA_LIST,
      params
    })
  }

  /**
   * 根据字典类型获取字典数据
   * @param dictType 字典类型
   * @returns 字典数据列表
   */
  static async getByType(dictType: string): Promise<DictData[]> {
    const response = await defHttp.get({
      url: `${API_PATHS.DICT_DATA_TYPE}/${dictType}`
    })
    return response.data || []
  }

  /**
   * 创建字典数据
   * @param data 创建数据
   * @returns 创建响应
   */
  static async create(data: DictDataCreateRequest): Promise<DictResponse> {
    return defHttp.post({
      url: API_PATHS.DICT_DATA_CREATE,
      data
    })
  }

  /**
   * 更新字典数据
   * @param id 字典数据ID
   * @param data 更新数据
   * @returns 更新响应
   */
  static async update(id: number, data: DictDataUpdateRequest): Promise<DictResponse> {
    return defHttp.put({
      url: `${API_PATHS.DICT_DATA_UPDATE}/${id}`,
      data
    })
  }

  /**
   * 删除字典数据
   * @param id 字典数据ID
   * @returns 删除响应
   */
  static async delete(id: number): Promise<DictResponse> {
    return defHttp.delete({
      url: `${API_PATHS.DICT_DATA_DELETE}/${id}`
    })
  }

  /**
   * 批量删除字典数据
   * @param ids 字典数据ID数组
   * @returns 删除响应
   */
  static async batchDelete(ids: number[]): Promise<DictResponse> {
    return defHttp.delete({
      url: API_PATHS.DICT_DATA_DELETE,
      data: { ids }
    })
  }

  /**
   * 获取字典数据详情
   * @param id 字典数据ID
   * @returns 字典数据详情
   */
  static async getDetail(id: number): Promise<DictResponse> {
    return defHttp.get({
      url: `${API_PATHS.DICT_DATA_DETAIL}/${id}`
    })
  }
}

/**
 * 字典缓存管理类
 */
export class DictCacheManager {
  private static cache = new Map<string, DictData[]>()
  private static cacheTime = new Map<string, number>()
  private static readonly CACHE_DURATION = 5 * 60 * 1000 // 5分钟缓存

  /**
   * 获取字典数据（带缓存）
   * @param dictType 字典类型
   * @param forceRefresh 是否强制刷新
   * @returns 字典数据列表
   */
  static async getDictData(dictType: string, forceRefresh = false): Promise<DictData[]> {
    const now = Date.now()
    const cacheKey = dictType
    const cachedTime = this.cacheTime.get(cacheKey) || 0

    // 检查缓存是否有效
    if (!forceRefresh && this.cache.has(cacheKey) && (now - cachedTime) < this.CACHE_DURATION) {
      return this.cache.get(cacheKey)!
    }

    try {
      // 从API获取数据
      const data = await DictDataApi.getByType(dictType)
      
      // 更新缓存
      this.cache.set(cacheKey, data)
      this.cacheTime.set(cacheKey, now)
      
      return data
    } catch (error) {
      console.error(`获取字典数据失败: ${dictType}`, error)
      // 如果API调用失败，返回缓存数据（如果有的话）
      return this.cache.get(cacheKey) || []
    }
  }

  /**
   * 清除指定字典类型的缓存
   * @param dictType 字典类型
   */
  static clearCache(dictType?: string): void {
    if (dictType) {
      this.cache.delete(dictType)
      this.cacheTime.delete(dictType)
    } else {
      // 清除所有缓存
      this.cache.clear()
      this.cacheTime.clear()
    }
  }

  /**
   * 获取字典标签
   * @param dictType 字典类型
   * @param dictValue 字典值
   * @returns 字典标签
   */
  static async getDictLabel(dictType: string, dictValue: string): Promise<string> {
    const dictData = await this.getDictData(dictType)
    const item = dictData.find(d => d.dictValue === dictValue)
    return item?.dictLabel || dictValue
  }

  /**
   * 获取字典选项（用于下拉选择）
   * @param dictType 字典类型
   * @returns 选项列表
   */
  static async getDictOptions(dictType: string): Promise<Array<{ label: string; value: string; color?: string }>> {
    const dictData = await this.getDictData(dictType)
    return dictData
      .filter(item => item.status === 1) // 只返回启用的选项
      .sort((a, b) => a.dictSort - b.dictSort) // 按排序字段排序
      .map(item => ({
        label: item.dictLabel,
        value: item.dictValue,
        color: item.listClass
      }))
  }
}

// 导出默认API实例
export const dictTypeApi = DictTypeApi
export const dictDataApi = DictDataApi
export const dictCache = DictCacheManager

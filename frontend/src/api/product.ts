/**
 * 产品管理API服务
 */

import { defHttp } from '@/utils/http/axios'
import type {
  Product,
  ProductCategory,
  ProductQueryParams,
  ProductCategoryQueryParams,
  ProductCreateRequest,
  ProductUpdateRequest,
  ProductCategoryCreateRequest,
  ProductCategoryUpdateRequest,
  ProductListResponse,
  ProductCategoryListResponse,
  ProductResponse
} from '@/types/product'

// API路径常量
const API_PATHS = {
  // 产品相关
  PRODUCT_LIST: '/product/list',
  PRODUCT_CREATE: '/product',
  PRODUCT_UPDATE: '/product',
  PRODUCT_DELETE: '/product',
  PRODUCT_DETAIL: '/product',
  PRODUCT_EXPORT: '/product/export',
  PRODUCT_IMPORT: '/product/import',
  PRODUCT_BATCH_UPDATE_STATUS: '/product/batch/status',
  PRODUCT_STOCK_ALERT: '/product/stock/alert',
  
  // 产品分类相关
  CATEGORY_LIST: '/product/category/list',
  CATEGORY_TREE: '/product/category/tree',
  CATEGORY_CREATE: '/product/category',
  CATEGORY_UPDATE: '/product/category',
  CATEGORY_DELETE: '/product/category',
  CATEGORY_DETAIL: '/product/category',
  CATEGORY_OPTIONS: '/product/category/options'
}

/**
 * 产品API服务类
 */
export class ProductApi {
  /**
   * 获取产品列表
   * @param params 查询参数
   * @returns 产品列表响应
   */
  static async getList(params?: ProductQueryParams): Promise<ProductListResponse> {
    return defHttp.get({
      url: API_PATHS.PRODUCT_LIST,
      params
    })
  }

  /**
   * 创建产品
   * @param data 创建数据
   * @returns 创建响应
   */
  static async create(data: ProductCreateRequest): Promise<ProductResponse> {
    return defHttp.post({
      url: API_PATHS.PRODUCT_CREATE,
      data
    })
  }

  /**
   * 更新产品
   * @param id 产品ID
   * @param data 更新数据
   * @returns 更新响应
   */
  static async update(id: number, data: ProductUpdateRequest): Promise<ProductResponse> {
    return defHttp.put({
      url: `${API_PATHS.PRODUCT_UPDATE}/${id}`,
      data
    })
  }

  /**
   * 删除产品
   * @param id 产品ID
   * @returns 删除响应
   */
  static async delete(id: number): Promise<ProductResponse> {
    return defHttp.delete({
      url: `${API_PATHS.PRODUCT_DELETE}/${id}`
    })
  }

  /**
   * 批量删除产品
   * @param ids 产品ID数组
   * @returns 删除响应
   */
  static async batchDelete(ids: number[]): Promise<ProductResponse> {
    return defHttp.delete({
      url: API_PATHS.PRODUCT_DELETE,
      data: { ids }
    })
  }

  /**
   * 获取产品详情
   * @param id 产品ID
   * @returns 产品详情
   */
  static async getDetail(id: number): Promise<ProductResponse> {
    return defHttp.get({
      url: `${API_PATHS.PRODUCT_DETAIL}/${id}`
    })
  }

  /**
   * 批量更新产品状态
   * @param ids 产品ID数组
   * @param status 状态
   * @returns 更新响应
   */
  static async batchUpdateStatus(ids: number[], status: number): Promise<ProductResponse> {
    return defHttp.put({
      url: API_PATHS.PRODUCT_BATCH_UPDATE_STATUS,
      data: { ids, status }
    })
  }

  /**
   * 获取库存预警产品列表
   * @returns 预警产品列表
   */
  static async getStockAlertList(): Promise<ProductListResponse> {
    return defHttp.get({
      url: API_PATHS.PRODUCT_STOCK_ALERT
    })
  }

  /**
   * 导出产品数据
   * @param params 查询参数
   * @returns 导出文件
   */
  static async export(params?: ProductQueryParams): Promise<Blob> {
    return defHttp.get({
      url: API_PATHS.PRODUCT_EXPORT,
      params,
      responseType: 'blob'
    })
  }

  /**
   * 导入产品数据
   * @param file 导入文件
   * @returns 导入响应
   */
  static async import(file: File): Promise<ProductResponse> {
    const formData = new FormData()
    formData.append('file', file)
    return defHttp.post({
      url: API_PATHS.PRODUCT_IMPORT,
      data: formData,
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  }

  /**
   * 生成产品编码
   * @param categoryId 分类ID
   * @returns 产品编码
   */
  static async generateCode(categoryId: number): Promise<string> {
    const response = await defHttp.get({
      url: `/product/generate-code/${categoryId}`
    })
    return response.data || ''
  }
}

/**
 * 产品分类API服务类
 */
export class ProductCategoryApi {
  /**
   * 获取产品分类列表
   * @param params 查询参数
   * @returns 分类列表响应
   */
  static async getList(params?: ProductCategoryQueryParams): Promise<ProductCategoryListResponse> {
    return defHttp.get({
      url: API_PATHS.CATEGORY_LIST,
      params
    })
  }

  /**
   * 获取产品分类树
   * @returns 分类树数据
   */
  static async getTree(): Promise<ProductCategory[]> {
    const response = await defHttp.get({
      url: API_PATHS.CATEGORY_TREE
    })
    return response.data || []
  }

  /**
   * 获取分类选项（用于下拉选择）
   * @returns 分类选项列表
   */
  static async getOptions(): Promise<Array<{ label: string; value: number; children?: any[] }>> {
    const response = await defHttp.get({
      url: API_PATHS.CATEGORY_OPTIONS
    })
    return response.data || []
  }

  /**
   * 创建产品分类
   * @param data 创建数据
   * @returns 创建响应
   */
  static async create(data: ProductCategoryCreateRequest): Promise<ProductResponse> {
    return defHttp.post({
      url: API_PATHS.CATEGORY_CREATE,
      data
    })
  }

  /**
   * 更新产品分类
   * @param id 分类ID
   * @param data 更新数据
   * @returns 更新响应
   */
  static async update(id: number, data: ProductCategoryUpdateRequest): Promise<ProductResponse> {
    return defHttp.put({
      url: `${API_PATHS.CATEGORY_UPDATE}/${id}`,
      data
    })
  }

  /**
   * 删除产品分类
   * @param id 分类ID
   * @returns 删除响应
   */
  static async delete(id: number): Promise<ProductResponse> {
    return defHttp.delete({
      url: `${API_PATHS.CATEGORY_DELETE}/${id}`
    })
  }

  /**
   * 获取产品分类详情
   * @param id 分类ID
   * @returns 分类详情
   */
  static async getDetail(id: number): Promise<ProductResponse> {
    return defHttp.get({
      url: `${API_PATHS.CATEGORY_DETAIL}/${id}`
    })
  }
}

/**
 * 产品库存管理类
 */
export class ProductStockManager {
  /**
   * 更新库存数量
   * @param productId 产品ID
   * @param quantity 数量变化（正数增加，负数减少）
   * @param type 变更类型
   * @param remark 备注
   * @returns 更新响应
   */
  static async updateStock(
    productId: number,
    quantity: number,
    type: 'IN' | 'OUT' | 'ADJUST',
    remark?: string
  ): Promise<ProductResponse> {
    return defHttp.post({
      url: '/product/stock/update',
      data: {
        productId,
        quantity,
        type,
        remark
      }
    })
  }

  /**
   * 获取库存变更记录
   * @param productId 产品ID
   * @param pageNum 页码
   * @param pageSize 每页数量
   * @returns 库存记录列表
   */
  static async getStockRecords(productId: number, pageNum = 1, pageSize = 10) {
    return defHttp.get({
      url: '/product/stock/records',
      params: {
        productId,
        pageNum,
        pageSize
      }
    })
  }

  /**
   * 批量调整库存
   * @param adjustments 调整数据数组
   * @returns 调整响应
   */
  static async batchAdjustStock(adjustments: Array<{
    productId: number
    quantity: number
    remark?: string
  }>): Promise<ProductResponse> {
    return defHttp.post({
      url: '/product/stock/batch-adjust',
      data: { adjustments }
    })
  }
}

/**
 * 产品图片管理类
 */
export class ProductImageManager {
  /**
   * 上传产品图片
   * @param file 图片文件
   * @returns 图片URL
   */
  static async uploadImage(file: File): Promise<string> {
    const formData = new FormData()
    formData.append('file', file)
    const response = await defHttp.post({
      url: '/product/image/upload',
      data: formData,
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
    return response.data?.url || ''
  }

  /**
   * 删除产品图片
   * @param imageUrl 图片URL
   * @returns 删除响应
   */
  static async deleteImage(imageUrl: string): Promise<ProductResponse> {
    return defHttp.delete({
      url: '/product/image/delete',
      data: { imageUrl }
    })
  }

  /**
   * 批量上传产品图片
   * @param files 图片文件数组
   * @returns 图片URL数组
   */
  static async batchUploadImages(files: File[]): Promise<string[]> {
    const formData = new FormData()
    files.forEach((file, index) => {
      formData.append(`files[${index}]`, file)
    })
    const response = await defHttp.post({
      url: '/product/image/batch-upload',
      data: formData,
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
    return response.data?.urls || []
  }
}

// 导出默认API实例
export const productApi = ProductApi
export const productCategoryApi = ProductCategoryApi
export const productStockManager = ProductStockManager
export const productImageManager = ProductImageManager

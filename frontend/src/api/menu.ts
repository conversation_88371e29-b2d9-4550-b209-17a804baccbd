/**
 * 菜单管理API服务
 */

import { defHttp } from '@/utils/http/axios'
import type {
  Menu,
  MenuQueryParams,
  MenuCreateRequest,
  MenuUpdateRequest,
  MenuListResponse,
  MenuDetailResponse,
  MenuOperationResponse,
  MenuTreeResponse
} from '@/types/menu'
import type { ApiResponse } from '@/types/common'

/**
 * 菜单管理API类
 */
export class MenuApi {
  private static readonly BASE_URL = '/api/system/menu'

  /**
   * 分页查询菜单列表
   * @param params 查询参数
   * @returns 菜单分页列表
   */
  static async getList(params?: MenuQueryParams): Promise<MenuListResponse> {
    return defHttp.get({
      url: MenuApi.BASE_URL,
      params
    })
  }

  /**
   * 查询所有菜单
   * @returns 所有菜单列表
   */
  static async getAll(): Promise<ApiResponse<Menu[]>> {
    return defHttp.get({
      url: `${MenuApi.BASE_URL}/all`
    })
  }

  /**
   * 查询菜单树
   * @returns 菜单树结构
   */
  static async getTree(): Promise<MenuTreeResponse> {
    return defHttp.get({
      url: `${MenuApi.BASE_URL}/tree`
    })
  }

  /**
   * 查询根菜单
   * @returns 根菜单列表
   */
  static async getRoot(): Promise<ApiResponse<Menu[]>> {
    return defHttp.get({
      url: `${MenuApi.BASE_URL}/root`
    })
  }

  /**
   * 根据菜单类型查询菜单
   * @param menuType 菜单类型
   * @returns 菜单列表
   */
  static async getByType(menuType: string): Promise<ApiResponse<Menu[]>> {
    return defHttp.get({
      url: `${MenuApi.BASE_URL}/type/${menuType}`
    })
  }

  /**
   * 根据ID查询菜单详情
   * @param id 菜单ID
   * @returns 菜单详情
   */
  static async getById(id: number): Promise<MenuDetailResponse> {
    return defHttp.get({
      url: `${MenuApi.BASE_URL}/${id}`
    })
  }

  /**
   * 根据路径查询菜单
   * @param path 菜单路径
   * @returns 菜单详情
   */
  static async getByPath(path: string): Promise<MenuDetailResponse> {
    return defHttp.get({
      url: `${MenuApi.BASE_URL}/path`,
      params: { path }
    })
  }

  /**
   * 查询子菜单
   * @param parentId 父菜单ID
   * @returns 子菜单列表
   */
  static async getChildren(parentId: number): Promise<ApiResponse<Menu[]>> {
    return defHttp.get({
      url: `${MenuApi.BASE_URL}/${parentId}/children`
    })
  }

  /**
   * 查询可见菜单
   * @returns 可见菜单列表
   */
  static async getVisible(): Promise<ApiResponse<Menu[]>> {
    return defHttp.get({
      url: `${MenuApi.BASE_URL}/visible`
    })
  }

  /**
   * 查询启用菜单
   * @returns 启用菜单列表
   */
  static async getEnabled(): Promise<ApiResponse<Menu[]>> {
    return defHttp.get({
      url: `${MenuApi.BASE_URL}/enabled`
    })
  }

  /**
   * 创建菜单
   * @param data 菜单创建数据
   * @returns 创建的菜单
   */
  static async create(data: MenuCreateRequest): Promise<MenuOperationResponse> {
    return defHttp.post({
      url: MenuApi.BASE_URL,
      data
    })
  }

  /**
   * 更新菜单
   * @param id 菜单ID
   * @param data 菜单更新数据
   * @returns 更新的菜单
   */
  static async update(id: number, data: MenuUpdateRequest): Promise<MenuOperationResponse> {
    return defHttp.put({
      url: `${MenuApi.BASE_URL}/${id}`,
      data
    })
  }

  /**
   * 删除菜单
   * @param id 菜单ID
   * @returns 删除结果
   */
  static async delete(id: number): Promise<ApiResponse<boolean>> {
    return defHttp.delete({
      url: `${MenuApi.BASE_URL}/${id}`
    })
  }

  /**
   * 批量删除菜单
   * @param ids 菜单ID列表
   * @returns 删除结果
   */
  static async batchDelete(ids: number[]): Promise<ApiResponse<boolean>> {
    return defHttp.delete({
      url: `${MenuApi.BASE_URL}/batch`,
      data: { ids }
    })
  }

  /**
   * 启用菜单
   * @param id 菜单ID
   * @returns 操作结果
   */
  static async enable(id: number): Promise<ApiResponse<boolean>> {
    return defHttp.put({
      url: `${MenuApi.BASE_URL}/${id}/enable`
    })
  }

  /**
   * 禁用菜单
   * @param id 菜单ID
   * @returns 操作结果
   */
  static async disable(id: number): Promise<ApiResponse<boolean>> {
    return defHttp.put({
      url: `${MenuApi.BASE_URL}/${id}/disable`
    })
  }

  /**
   * 显示菜单
   * @param id 菜单ID
   * @returns 操作结果
   */
  static async show(id: number): Promise<ApiResponse<boolean>> {
    return defHttp.put({
      url: `${MenuApi.BASE_URL}/${id}/show`
    })
  }

  /**
   * 隐藏菜单
   * @param id 菜单ID
   * @returns 操作结果
   */
  static async hide(id: number): Promise<ApiResponse<boolean>> {
    return defHttp.put({
      url: `${MenuApi.BASE_URL}/${id}/hide`
    })
  }

  /**
   * 移动菜单
   * @param id 菜单ID
   * @param targetParentId 目标父菜单ID
   * @param position 位置
   * @returns 操作结果
   */
  static async move(id: number, targetParentId: number, position: number): Promise<ApiResponse<boolean>> {
    return defHttp.put({
      url: `${MenuApi.BASE_URL}/${id}/move`,
      data: { targetParentId, position }
    })
  }

  /**
   * 复制菜单
   * @param id 菜单ID
   * @param targetParentId 目标父菜单ID
   * @returns 复制的菜单
   */
  static async copy(id: number, targetParentId: number): Promise<MenuOperationResponse> {
    return defHttp.post({
      url: `${MenuApi.BASE_URL}/${id}/copy`,
      data: { targetParentId }
    })
  }

  /**
   * 检查菜单路径是否存在
   * @param path 菜单路径
   * @param excludeId 排除的菜单ID
   * @returns 是否存在
   */
  static async checkPath(path: string, excludeId?: number): Promise<ApiResponse<boolean>> {
    return defHttp.get({
      url: `${MenuApi.BASE_URL}/check-path`,
      params: { path, excludeId }
    })
  }

  /**
   * 检查权限标识是否存在
   * @param permission 权限标识
   * @param excludeId 排除的菜单ID
   * @returns 是否存在
   */
  static async checkPermission(permission: string, excludeId?: number): Promise<ApiResponse<boolean>> {
    return defHttp.get({
      url: `${MenuApi.BASE_URL}/check-permission`,
      params: { permission, excludeId }
    })
  }

  /**
   * 获取菜单统计信息
   * @returns 统计信息
   */
  static async getStats(): Promise<ApiResponse<{
    total: number
    directoryCount: number
    menuCount: number
    buttonCount: number
    enabledCount: number
    disabledCount: number
    visibleCount: number
    hiddenCount: number
  }>> {
    return defHttp.get({
      url: `${MenuApi.BASE_URL}/stats`
    })
  }

  /**
   * 获取用户菜单
   * @param userId 用户ID
   * @returns 用户菜单树
   */
  static async getUserMenus(userId?: number): Promise<MenuTreeResponse> {
    return defHttp.get({
      url: `${MenuApi.BASE_URL}/user-menus`,
      params: userId ? { userId } : undefined
    })
  }

  /**
   * 刷新菜单缓存
   * @returns 操作结果
   */
  static async refreshCache(): Promise<ApiResponse<boolean>> {
    return defHttp.post({
      url: `${MenuApi.BASE_URL}/refresh-cache`
    })
  }
}

export default MenuApi

/**
 * 角色管理API服务
 * 基于后端Swagger API文档实现
 */

import { defHttp } from '@/utils/http/axios'
import type {
  Role,
  RoleCreateRequest,
  RoleUpdateRequest,
  RolePermissionRequest,
  RoleQueryParams,
  RoleListResponse,
  RoleDetailResponse,
  RoleOperationResponse
} from '@/types/role'
import type { ApiResponse } from '@/types/common'

/**
 * 角色管理API类
 */
export class RoleApi {
  private static readonly BASE_URL = '/api/roles'

  /**
   * 分页查询角色列表
   * @param params 查询参数
   * @returns 角色分页列表
   */
  static async getList(params?: RoleQueryParams): Promise<RoleListResponse> {
    return defHttp.get({
      url: RoleApi.BASE_URL,
      params
    })
  }

  /**
   * 查询所有角色
   * @returns 所有角色列表
   */
  static async getAll(): Promise<ApiResponse<Role[]>> {
    return defHttp.get({
      url: `${RoleApi.BASE_URL}/all`
    })
  }

  /**
   * 查询启用的角色
   * @returns 启用的角色列表
   */
  static async getEnabled(): Promise<ApiResponse<Role[]>> {
    return defHttp.get({
      url: `${RoleApi.BASE_URL}/enabled`
    })
  }

  /**
   * 查询默认角色
   * @returns 默认角色列表
   */
  static async getDefault(): Promise<ApiResponse<Role[]>> {
    return defHttp.get({
      url: `${RoleApi.BASE_URL}/default`
    })
  }

  /**
   * 根据角色类型查询角色
   * @param roleType 角色类型
   * @returns 角色列表
   */
  static async getByType(roleType: number): Promise<ApiResponse<Role[]>> {
    return defHttp.get({
      url: `${RoleApi.BASE_URL}/type/${roleType}`
    })
  }

  /**
   * 根据ID查询角色详情
   * @param id 角色ID
   * @returns 角色详情
   */
  static async getById(id: number): Promise<RoleDetailResponse> {
    return defHttp.get({
      url: `${RoleApi.BASE_URL}/${id}`
    })
  }

  /**
   * 根据角色编码查询角色
   * @param roleCode 角色编码
   * @returns 角色详情
   */
  static async getByCode(roleCode: string): Promise<RoleDetailResponse> {
    return defHttp.get({
      url: `${RoleApi.BASE_URL}/code/${roleCode}`
    })
  }

  /**
   * 创建角色
   * @param data 角色创建数据
   * @returns 创建的角色
   */
  static async create(data: RoleCreateRequest): Promise<RoleOperationResponse> {
    return defHttp.post({
      url: RoleApi.BASE_URL,
      data
    })
  }

  /**
   * 更新角色
   * @param id 角色ID
   * @param data 角色更新数据
   * @returns 更新的角色
   */
  static async update(id: number, data: RoleUpdateRequest): Promise<RoleOperationResponse> {
    return defHttp.put({
      url: `${RoleApi.BASE_URL}/${id}`,
      data
    })
  }

  /**
   * 删除角色
   * @param id 角色ID
   * @returns 删除结果
   */
  static async delete(id: number): Promise<ApiResponse<void>> {
    return defHttp.delete({
      url: `${RoleApi.BASE_URL}/${id}`
    })
  }

  /**
   * 批量删除角色
   * @param ids 角色ID列表
   * @returns 删除结果
   */
  static async batchDelete(ids: number[]): Promise<ApiResponse<void>> {
    return defHttp.delete({
      url: `${RoleApi.BASE_URL}/batch`,
      data: ids
    })
  }

  /**
   * 启用角色
   * @param id 角色ID
   * @returns 操作结果
   */
  static async enable(id: number): Promise<ApiResponse<void>> {
    return defHttp.put({
      url: `${RoleApi.BASE_URL}/${id}/enable`
    })
  }

  /**
   * 禁用角色
   * @param id 角色ID
   * @returns 操作结果
   */
  static async disable(id: number): Promise<ApiResponse<void>> {
    return defHttp.put({
      url: `${RoleApi.BASE_URL}/${id}/disable`
    })
  }

  /**
   * 检查角色编码是否存在
   * @param roleCode 角色编码
   * @param excludeId 排除的角色ID
   * @returns 是否存在
   */
  static async checkCode(roleCode: string, excludeId?: number): Promise<ApiResponse<boolean>> {
    return defHttp.get({
      url: `${RoleApi.BASE_URL}/check-code`,
      params: { roleCode, excludeId }
    })
  }

  /**
   * 统计角色数量
   * @returns 角色总数
   */
  static async count(): Promise<ApiResponse<number>> {
    return defHttp.get({
      url: `${RoleApi.BASE_URL}/count`
    })
  }

  /**
   * 查询角色权限
   * @param id 角色ID
   * @returns 权限编码列表
   */
  static async getPermissions(id: number): Promise<ApiResponse<string[]>> {
    return defHttp.get({
      url: `${RoleApi.BASE_URL}/${id}/permissions`
    })
  }

  /**
   * 检查角色是否拥有指定权限
   * @param id 角色ID
   * @param permissionCode 权限编码
   * @returns 是否拥有权限
   */
  static async checkPermission(id: number, permissionCode: string): Promise<ApiResponse<boolean>> {
    return defHttp.get({
      url: `${RoleApi.BASE_URL}/${id}/permissions/${permissionCode}`
    })
  }

  /**
   * 分配权限给角色
   * @param data 权限分配数据
   * @returns 操作结果
   */
  static async grantPermissions(data: RolePermissionRequest): Promise<ApiResponse<void>> {
    return defHttp.post({
      url: `${RoleApi.BASE_URL}/permissions`,
      data
    })
  }

  /**
   * 撤销角色权限
   * @param data 权限撤销数据
   * @returns 操作结果
   */
  static async revokePermissions(data: RolePermissionRequest): Promise<ApiResponse<void>> {
    return defHttp.delete({
      url: `${RoleApi.BASE_URL}/permissions`,
      data
    })
  }
}

// 导出默认实例
export default RoleApi

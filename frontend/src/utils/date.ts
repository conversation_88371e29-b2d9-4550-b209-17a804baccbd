/**
 * 日期时间工具函数
 * 提供常用的日期格式化、解析和计算功能
 */

import dayjs from 'dayjs'
import 'dayjs/locale/zh-cn'
import relativeTime from 'dayjs/plugin/relativeTime'
import utc from 'dayjs/plugin/utc'
import timezone from 'dayjs/plugin/timezone'

// 配置dayjs插件
dayjs.extend(relativeTime)
dayjs.extend(utc)
dayjs.extend(timezone)
dayjs.locale('zh-cn')

/**
 * 格式化日期时间
 * @param date 日期对象、时间戳或日期字符串
 * @param format 格式化模板，默认为 'YYYY-MM-DD HH:mm:ss'
 * @returns 格式化后的日期字符串
 */
export function formatDateTime(
  date: Date | string | number | null | undefined,
  format: string = 'YYYY-MM-DD HH:mm:ss'
): string {
  if (!date) return ''
  return dayjs(date).format(format)
}

/**
 * 格式化日期
 * @param date 日期对象、时间戳或日期字符串
 * @param format 格式化模板，默认为 'YYYY-MM-DD'
 * @returns 格式化后的日期字符串
 */
export function formatDate(
  date: Date | string | number | null | undefined,
  format: string = 'YYYY-MM-DD'
): string {
  if (!date) return ''
  return dayjs(date).format(format)
}

/**
 * 格式化时间
 * @param date 日期对象、时间戳或日期字符串
 * @param format 格式化模板，默认为 'HH:mm:ss'
 * @returns 格式化后的时间字符串
 */
export function formatTime(
  date: Date | string | number | null | undefined,
  format: string = 'HH:mm:ss'
): string {
  if (!date) return ''
  return dayjs(date).format(format)
}

/**
 * 获取相对时间
 * @param date 日期对象、时间戳或日期字符串
 * @returns 相对时间字符串，如 '2小时前'、'3天前'
 */
export function getRelativeTime(
  date: Date | string | number | null | undefined
): string {
  if (!date) return ''
  return dayjs(date).fromNow()
}

/**
 * 判断是否为今天
 * @param date 日期对象、时间戳或日期字符串
 * @returns 是否为今天
 */
export function isToday(date: Date | string | number | null | undefined): boolean {
  if (!date) return false
  return dayjs(date).isSame(dayjs(), 'day')
}

/**
 * 判断是否为昨天
 * @param date 日期对象、时间戳或日期字符串
 * @returns 是否为昨天
 */
export function isYesterday(date: Date | string | number | null | undefined): boolean {
  if (!date) return false
  return dayjs(date).isSame(dayjs().subtract(1, 'day'), 'day')
}

/**
 * 判断是否为本周
 * @param date 日期对象、时间戳或日期字符串
 * @returns 是否为本周
 */
export function isThisWeek(date: Date | string | number | null | undefined): boolean {
  if (!date) return false
  return dayjs(date).isSame(dayjs(), 'week')
}

/**
 * 判断是否为本月
 * @param date 日期对象、时间戳或日期字符串
 * @returns 是否为本月
 */
export function isThisMonth(date: Date | string | number | null | undefined): boolean {
  if (!date) return false
  return dayjs(date).isSame(dayjs(), 'month')
}

/**
 * 判断是否为本年
 * @param date 日期对象、时间戳或日期字符串
 * @returns 是否为本年
 */
export function isThisYear(date: Date | string | number | null | undefined): boolean {
  if (!date) return false
  return dayjs(date).isSame(dayjs(), 'year')
}

/**
 * 获取日期范围
 * @param startDate 开始日期
 * @param endDate 结束日期
 * @returns 日期范围数组
 */
export function getDateRange(
  startDate: Date | string | number,
  endDate: Date | string | number
): string[] {
  const start = dayjs(startDate)
  const end = dayjs(endDate)
  const dates: string[] = []
  
  let current = start
  while (current.isBefore(end) || current.isSame(end, 'day')) {
    dates.push(current.format('YYYY-MM-DD'))
    current = current.add(1, 'day')
  }
  
  return dates
}

/**
 * 计算两个日期之间的天数差
 * @param date1 日期1
 * @param date2 日期2
 * @returns 天数差（正数表示date1在date2之后）
 */
export function getDaysDiff(
  date1: Date | string | number,
  date2: Date | string | number
): number {
  return dayjs(date1).diff(dayjs(date2), 'day')
}

/**
 * 计算两个日期之间的小时差
 * @param date1 日期1
 * @param date2 日期2
 * @returns 小时差
 */
export function getHoursDiff(
  date1: Date | string | number,
  date2: Date | string | number
): number {
  return dayjs(date1).diff(dayjs(date2), 'hour')
}

/**
 * 计算两个日期之间的分钟差
 * @param date1 日期1
 * @param date2 日期2
 * @returns 分钟差
 */
export function getMinutesDiff(
  date1: Date | string | number,
  date2: Date | string | number
): number {
  return dayjs(date1).diff(dayjs(date2), 'minute')
}

/**
 * 获取当前时间戳（毫秒）
 * @returns 当前时间戳
 */
export function getCurrentTimestamp(): number {
  return dayjs().valueOf()
}

/**
 * 获取当前时间戳（秒）
 * @returns 当前时间戳（秒）
 */
export function getCurrentTimestampSeconds(): number {
  return Math.floor(dayjs().valueOf() / 1000)
}

/**
 * 将时间戳转换为日期对象
 * @param timestamp 时间戳（毫秒或秒）
 * @returns 日期对象
 */
export function timestampToDate(timestamp: number): Date {
  // 如果时间戳小于10位，认为是秒级时间戳，需要转换为毫秒
  const ts = timestamp.toString().length <= 10 ? timestamp * 1000 : timestamp
  return dayjs(ts).toDate()
}

/**
 * 获取月份的第一天
 * @param date 日期
 * @returns 月份第一天的日期字符串
 */
export function getMonthStart(date?: Date | string | number): string {
  return dayjs(date).startOf('month').format('YYYY-MM-DD')
}

/**
 * 获取月份的最后一天
 * @param date 日期
 * @returns 月份最后一天的日期字符串
 */
export function getMonthEnd(date?: Date | string | number): string {
  return dayjs(date).endOf('month').format('YYYY-MM-DD')
}

/**
 * 获取周的第一天（周一）
 * @param date 日期
 * @returns 周第一天的日期字符串
 */
export function getWeekStart(date?: Date | string | number): string {
  return dayjs(date).startOf('week').format('YYYY-MM-DD')
}

/**
 * 获取周的最后一天（周日）
 * @param date 日期
 * @returns 周最后一天的日期字符串
 */
export function getWeekEnd(date?: Date | string | number): string {
  return dayjs(date).endOf('week').format('YYYY-MM-DD')
}

/**
 * 智能格式化日期时间
 * 根据时间距离现在的长短，选择合适的显示格式
 * @param date 日期
 * @returns 格式化后的字符串
 */
export function smartFormatDateTime(
  date: Date | string | number | null | undefined
): string {
  if (!date) return ''
  
  const target = dayjs(date)
  const now = dayjs()
  
  // 如果是今天，显示时间
  if (target.isSame(now, 'day')) {
    return target.format('HH:mm')
  }
  
  // 如果是昨天，显示"昨天 HH:mm"
  if (target.isSame(now.subtract(1, 'day'), 'day')) {
    return `昨天 ${target.format('HH:mm')}`
  }
  
  // 如果是本年，显示"MM-DD HH:mm"
  if (target.isSame(now, 'year')) {
    return target.format('MM-DD HH:mm')
  }
  
  // 其他情况显示完整日期时间
  return target.format('YYYY-MM-DD HH:mm')
}

/**
 * 验证日期字符串格式
 * @param dateString 日期字符串
 * @param format 期望的格式
 * @returns 是否符合格式
 */
export function isValidDateFormat(
  dateString: string,
  format: string = 'YYYY-MM-DD'
): boolean {
  return dayjs(dateString, format, true).isValid()
}

/**
 * 获取指定日期的年龄
 * @param birthDate 出生日期
 * @returns 年龄
 */
export function getAge(birthDate: Date | string | number): number {
  return dayjs().diff(dayjs(birthDate), 'year')
}

// 导出dayjs实例，供其他地方使用
export { dayjs }

// 默认导出常用函数
export default {
  formatDateTime,
  formatDate,
  formatTime,
  getRelativeTime,
  isToday,
  isYesterday,
  isThisWeek,
  isThisMonth,
  isThisYear,
  getDateRange,
  getDaysDiff,
  getHoursDiff,
  getMinutesDiff,
  getCurrentTimestamp,
  getCurrentTimestampSeconds,
  timestampToDate,
  getMonthStart,
  getMonthEnd,
  getWeekStart,
  getWeekEnd,
  smartFormatDateTime,
  isValidDateFormat,
  getAge,
  dayjs
}

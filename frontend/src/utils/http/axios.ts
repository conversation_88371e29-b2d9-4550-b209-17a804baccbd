/**
 * HTTP请求工具类
 * 基于axios封装，支持JWT认证、多租户、请求拦截等功能
 */
import axios from 'axios'
import type { AxiosInstance, AxiosRequestConfig, AxiosResponse, AxiosError } from 'axios'
import { message } from 'ant-design-vue'

// 请求响应接口定义
export interface ApiResponse<T = any> {
  code: string
  message: string
  data: T
  timestamp?: number
}

// 请求配置接口
export interface RequestOptions extends AxiosRequestConfig {
  // 是否显示加载提示
  showLoading?: boolean
  // 是否显示错误提示
  showError?: boolean
  // 是否需要认证
  requireAuth?: boolean
  // 自定义错误处理
  errorHandler?: (error: AxiosError) => void
}

class HttpRequest {
  private axiosInstance: AxiosInstance
  private readonly baseURL: string
  private readonly timeout: number

  constructor() {
    // 从环境变量获取配置
    this.baseURL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8081'
    this.timeout = Number(import.meta.env.VITE_API_TIMEOUT) || 10000

    // 创建axios实例
    this.axiosInstance = axios.create({
      baseURL: this.baseURL,
      timeout: this.timeout,
      headers: {
        'Content-Type': 'application/json;charset=UTF-8'
      }
    })

    // 设置请求拦截器
    this.setupRequestInterceptors()
    // 设置响应拦截器
    this.setupResponseInterceptors()
  }

  /**
   * 设置请求拦截器
   */
  private setupRequestInterceptors(): void {
    this.axiosInstance.interceptors.request.use(
      (config) => {
        // 添加JWT token到请求头
        const token = this.getToken()
        if (token && config.headers) {
          config.headers.Authorization = `Bearer ${token}`
        }

        // 添加租户ID到请求头（如果存在）
        const tenantId = this.getTenantId()
        if (tenantId && config.headers) {
          config.headers['X-Tenant-Id'] = tenantId
        }

        // 添加请求时间戳
        config.headers = {
          ...config.headers,
          'X-Request-Time': Date.now().toString()
        }

        // 开发环境下打印请求信息
        if (import.meta.env.DEV) {
          console.log('🚀 HTTP Request:', {
            url: config.url,
            method: config.method?.toUpperCase(),
            params: config.params,
            data: config.data,
            headers: config.headers
          })
        }

        return config
      },
      (error: AxiosError) => {
        console.error('❌ Request Error:', error)
        return Promise.reject(error)
      }
    )
  }

  /**
   * 设置响应拦截器
   */
  private setupResponseInterceptors(): void {
    this.axiosInstance.interceptors.response.use(
      (response: AxiosResponse<ApiResponse>) => {
        const { data } = response

        // 开发环境下打印响应信息
        if (import.meta.env.DEV) {
          const requestTime = response.config.headers?.['X-Request-Time']
          const responseTime = Date.now()
          const duration = requestTime ? responseTime - Number(requestTime) : 0

          console.log('✅ HTTP Response:', {
            url: response.config.url,
            status: response.status,
            duration: `${duration}ms`,
            data: data
          })
        }

        // 检查业务状态码
        if (data.code === '200') {
          return response
        } else {
          // 业务错误处理
          this.handleBusinessError(data)
          return Promise.reject(new Error(data.message || '请求失败'))
        }
      },
      (error: AxiosError) => {
        // HTTP错误处理
        this.handleHttpError(error)
        return Promise.reject(error)
      }
    )
  }

  /**
   * 处理业务错误
   */
  private handleBusinessError(data: ApiResponse): void {
    const { code, message: msg } = data

    switch (code) {
      case '401':
        message.error('登录已过期，请重新登录')
        this.handleTokenExpired()
        break
      case '403':
        message.error('没有权限访问该资源')
        break
      case '404':
        message.error('请求的资源不存在')
        break
      case '500':
        message.error('服务器内部错误')
        break
      default:
        message.error(msg || '请求失败')
    }
  }

  /**
   * 处理HTTP错误
   */
  private handleHttpError(error: AxiosError): void {
    const { response, message: msg } = error

    if (response) {
      const { status, statusText } = response

      switch (status) {
        case 401:
          message.error('认证失败，请重新登录')
          this.handleTokenExpired()
          break
        case 403:
          message.error('访问被拒绝')
          break
        case 404:
          message.error('请求地址不存在')
          break
        case 500:
          message.error('服务器错误')
          break
        case 502:
          message.error('网关错误')
          break
        case 503:
          message.error('服务不可用')
          break
        case 504:
          message.error('网关超时')
          break
        default:
          message.error(`请求失败: ${status} ${statusText}`)
      }
    } else if (msg.includes('timeout')) {
      message.error('请求超时，请稍后重试')
    } else if (msg.includes('Network Error')) {
      message.error('网络连接失败，请检查网络')
    } else {
      message.error('请求失败，请稍后重试')
    }
  }

  /**
   * 处理token过期
   */
  private handleTokenExpired(): void {
    // 清除本地存储的认证信息
    localStorage.removeItem('token')
    localStorage.removeItem('refreshToken')
    localStorage.removeItem('userInfo')

    // 跳转到登录页面
    window.location.href = '/login'
  }

  /**
   * 获取存储的token
   */
  private getToken(): string | null {
    return localStorage.getItem('token')
  }

  /**
   * 获取租户ID
   */
  private getTenantId(): string | null {
    const userInfo = localStorage.getItem('userInfo')
    if (userInfo) {
      try {
        const user = JSON.parse(userInfo)
        return user.tenantId?.toString() || null
      } catch {
        return null
      }
    }
    return null
  }

  /**
   * GET请求
   */
  public get<T = any>(config: RequestOptions): Promise<ApiResponse<T>> {
    return this.request<T>({ ...config, method: 'GET' })
  }

  /**
   * POST请求
   */
  public post<T = any>(config: RequestOptions): Promise<ApiResponse<T>> {
    return this.request<T>({ ...config, method: 'POST' })
  }

  /**
   * PUT请求
   */
  public put<T = any>(config: RequestOptions): Promise<ApiResponse<T>> {
    return this.request<T>({ ...config, method: 'PUT' })
  }

  /**
   * DELETE请求
   */
  public delete<T = any>(config: RequestOptions): Promise<ApiResponse<T>> {
    return this.request<T>({ ...config, method: 'DELETE' })
  }

  /**
   * 通用请求方法
   */
  public async request<T = any>(config: RequestOptions): Promise<ApiResponse<T>> {
    try {
      const response = await this.axiosInstance.request<ApiResponse<T>>(config)
      return response.data
    } catch (error) {
      // 如果有自定义错误处理器，则调用
      if (config.errorHandler && error instanceof AxiosError) {
        config.errorHandler(error)
      }
      throw error
    }
  }

  /**
   * 上传文件
   */
  public upload<T = any>(config: RequestOptions & { file: File }): Promise<ApiResponse<T>> {
    const formData = new FormData()
    formData.append('file', config.file)

    return this.request<T>({
      ...config,
      method: 'POST',
      data: formData,
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  }
}

// 创建HTTP请求实例
export const defHttp = new HttpRequest()

// 导出类型
export type { AxiosRequestConfig, AxiosResponse, AxiosError }

/**
 * 支付应用配置API接口
 * @description 支付应用、渠道配置、密钥管理等相关的API接口定义
 */

import type { AxiosResponse } from 'axios'
import type {
  PaymentAppConfig,
  PaymentAppQueryParams,
  PaymentAppFormData,
  PaymentChannelConfig,
  KeyInfo,
  KeyFormData,
  ChannelTestParams,
  ChannelTestResult,
  ApiResponse,
  PageResponse
} from '../types/payment-app'

/**
 * 支付应用配置API类
 */
export class PaymentAppApi {
  private baseUrl = '/api/payment/apps'

  /**
   * 获取支付应用列表
   * @param params 查询参数
   * @returns 支付应用分页列表
   */
  async getPaymentApps(params?: PaymentAppQueryParams): Promise<ApiResponse<PageResponse<PaymentAppConfig>>> {
    const response: AxiosResponse<ApiResponse<PageResponse<PaymentAppConfig>>> = await axios.get(this.baseUrl, {
      params
    })
    return response.data
  }

  /**
   * 获取支付应用详情
   * @param id 应用ID
   * @returns 支付应用详情
   */
  async getPaymentApp(id: string): Promise<ApiResponse<PaymentAppConfig>> {
    const response: AxiosResponse<ApiResponse<PaymentAppConfig>> = await axios.get(`${this.baseUrl}/${id}`)
    return response.data
  }

  /**
   * 创建支付应用
   * @param data 应用配置数据
   * @returns 创建的支付应用
   */
  async createPaymentApp(data: PaymentAppFormData): Promise<ApiResponse<PaymentAppConfig>> {
    const response: AxiosResponse<ApiResponse<PaymentAppConfig>> = await axios.post(this.baseUrl, data)
    return response.data
  }

  /**
   * 更新支付应用
   * @param id 应用ID
   * @param data 应用配置数据
   * @returns 更新的支付应用
   */
  async updatePaymentApp(id: string, data: Partial<PaymentAppFormData>): Promise<ApiResponse<PaymentAppConfig>> {
    const response: AxiosResponse<ApiResponse<PaymentAppConfig>> = await axios.put(`${this.baseUrl}/${id}`, data)
    return response.data
  }

  /**
   * 删除支付应用
   * @param id 应用ID
   * @returns 删除结果
   */
  async deletePaymentApp(id: string): Promise<ApiResponse<void>> {
    const response: AxiosResponse<ApiResponse<void>> = await axios.delete(`${this.baseUrl}/${id}`)
    return response.data
  }

  /**
   * 启用/禁用支付应用
   * @param id 应用ID
   * @param enabled 是否启用
   * @returns 操作结果
   */
  async togglePaymentApp(id: string, enabled: boolean): Promise<ApiResponse<void>> {
    const response: AxiosResponse<ApiResponse<void>> = await axios.patch(`${this.baseUrl}/${id}/toggle`, {
      enabled
    })
    return response.data
  }

  /**
   * 测试支付渠道连接
   * @param params 测试参数
   * @returns 测试结果
   */
  async testChannel(params: ChannelTestParams): Promise<ApiResponse<ChannelTestResult>> {
    const response: AxiosResponse<ApiResponse<ChannelTestResult>> = await axios.post(
      `${this.baseUrl}/${params.appId}/channels/${params.channelId}/test`,
      params
    )
    return response.data
  }

  /**
   * 批量测试支付渠道
   * @param appId 应用ID
   * @returns 批量测试结果
   */
  async batchTestChannels(appId: string): Promise<ApiResponse<ChannelTestResult[]>> {
    const response: AxiosResponse<ApiResponse<ChannelTestResult[]>> = await axios.post(
      `${this.baseUrl}/${appId}/channels/batch-test`
    )
    return response.data
  }

  /**
   * 获取支付渠道配置
   * @param appId 应用ID
   * @returns 支付渠道列表
   */
  async getChannels(appId: string): Promise<ApiResponse<PaymentChannelConfig[]>> {
    const response: AxiosResponse<ApiResponse<PaymentChannelConfig[]>> = await axios.get(
      `${this.baseUrl}/${appId}/channels`
    )
    return response.data
  }

  /**
   * 更新支付渠道配置
   * @param appId 应用ID
   * @param channelId 渠道ID
   * @param data 渠道配置数据
   * @returns 更新的渠道配置
   */
  async updateChannel(
    appId: string,
    channelId: string,
    data: Partial<PaymentChannelConfig>
  ): Promise<ApiResponse<PaymentChannelConfig>> {
    const response: AxiosResponse<ApiResponse<PaymentChannelConfig>> = await axios.put(
      `${this.baseUrl}/${appId}/channels/${channelId}`,
      data
    )
    return response.data
  }

  /**
   * 启用/禁用支付渠道
   * @param appId 应用ID
   * @param channelId 渠道ID
   * @param enabled 是否启用
   * @returns 操作结果
   */
  async toggleChannel(appId: string, channelId: string, enabled: boolean): Promise<ApiResponse<void>> {
    const response: AxiosResponse<ApiResponse<void>> = await axios.patch(
      `${this.baseUrl}/${appId}/channels/${channelId}/toggle`,
      { enabled }
    )
    return response.data
  }
}

/**
 * 密钥管理API类
 */
export class KeyManagementApi {
  private baseUrl = '/api/payment/keys'

  /**
   * 获取密钥列表
   * @param appId 应用ID
   * @returns 密钥列表
   */
  async getKeys(appId: string): Promise<ApiResponse<KeyInfo[]>> {
    const response: AxiosResponse<ApiResponse<KeyInfo[]>> = await axios.get(this.baseUrl, {
      params: { appId }
    })
    return response.data
  }

  /**
   * 获取密钥详情
   * @param id 密钥ID
   * @returns 密钥详情
   */
  async getKey(id: string): Promise<ApiResponse<KeyInfo>> {
    const response: AxiosResponse<ApiResponse<KeyInfo>> = await axios.get(`${this.baseUrl}/${id}`)
    return response.data
  }

  /**
   * 创建密钥
   * @param data 密钥数据
   * @returns 创建的密钥
   */
  async createKey(data: KeyFormData): Promise<ApiResponse<KeyInfo>> {
    const response: AxiosResponse<ApiResponse<KeyInfo>> = await axios.post(this.baseUrl, data)
    return response.data
  }

  /**
   * 更新密钥
   * @param id 密钥ID
   * @param data 密钥数据
   * @returns 更新的密钥
   */
  async updateKey(id: string, data: Partial<KeyFormData>): Promise<ApiResponse<KeyInfo>> {
    const response: AxiosResponse<ApiResponse<KeyInfo>> = await axios.put(`${this.baseUrl}/${id}`, data)
    return response.data
  }

  /**
   * 删除密钥
   * @param id 密钥ID
   * @returns 删除结果
   */
  async deleteKey(id: string): Promise<ApiResponse<void>> {
    const response: AxiosResponse<ApiResponse<void>> = await axios.delete(`${this.baseUrl}/${id}`)
    return response.data
  }

  /**
   * 启用/禁用密钥
   * @param id 密钥ID
   * @param enabled 是否启用
   * @returns 操作结果
   */
  async toggleKey(id: string, enabled: boolean): Promise<ApiResponse<void>> {
    const response: AxiosResponse<ApiResponse<void>> = await axios.patch(`${this.baseUrl}/${id}/toggle`, {
      enabled
    })
    return response.data
  }

  /**
   * 轮换密钥
   * @param id 密钥ID
   * @returns 新密钥
   */
  async rotateKey(id: string): Promise<ApiResponse<KeyInfo>> {
    const response: AxiosResponse<ApiResponse<KeyInfo>> = await axios.post(`${this.baseUrl}/${id}/rotate`)
    return response.data
  }

  /**
   * 生成密钥对
   * @param algorithm 算法类型
   * @returns 密钥对
   */
  async generateKeyPair(algorithm: string): Promise<ApiResponse<{ publicKey: string; privateKey: string }>> {
    const response: AxiosResponse<ApiResponse<{ publicKey: string; privateKey: string }>> = await axios.post(
      `${this.baseUrl}/generate-pair`,
      { algorithm }
    )
    return response.data
  }

  /**
   * 验证密钥
   * @param id 密钥ID
   * @returns 验证结果
   */
  async validateKey(id: string): Promise<ApiResponse<{ valid: boolean; message?: string }>> {
    const response: AxiosResponse<ApiResponse<{ valid: boolean; message?: string }>> = await axios.post(
      `${this.baseUrl}/${id}/validate`
    )
    return response.data
  }
}

/**
 * 支付渠道API类
 */
export class PaymentChannelApi {
  private baseUrl = '/api/payment/channels'

  /**
   * 获取支持的支付渠道列表
   * @returns 支付渠道列表
   */
  async getSupportedChannels(): Promise<ApiResponse<PaymentChannelConfig[]>> {
    const response: AxiosResponse<ApiResponse<PaymentChannelConfig[]>> = await axios.get(`${this.baseUrl}/supported`)
    return response.data
  }

  /**
   * 获取渠道配置模板
   * @param channelType 渠道类型
   * @returns 配置模板
   */
  async getChannelTemplate(channelType: string): Promise<ApiResponse<Record<string, any>>> {
    const response: AxiosResponse<ApiResponse<Record<string, any>>> = await axios.get(
      `${this.baseUrl}/template/${channelType}`
    )
    return response.data
  }

  /**
   * 验证渠道配置
   * @param channelType 渠道类型
   * @param config 渠道配置
   * @returns 验证结果
   */
  async validateChannelConfig(
    channelType: string,
    config: Record<string, any>
  ): Promise<ApiResponse<{ valid: boolean; errors?: string[] }>> {
    const response: AxiosResponse<ApiResponse<{ valid: boolean; errors?: string[] }>> = await axios.post(
      `${this.baseUrl}/validate/${channelType}`,
      config
    )
    return response.data
  }
}

// 导出API实例
export const paymentAppApi = new PaymentAppApi()
export const keyManagementApi = new KeyManagementApi()
export const paymentChannelApi = new PaymentChannelApi()

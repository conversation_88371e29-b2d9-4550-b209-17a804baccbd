<template>
  <div class="login-test-container">
    <h2>登录功能测试</h2>
    
    <div class="test-form">
      <div class="form-item">
        <label>API地址:</label>
        <input v-model="apiUrl" type="text" />
      </div>
      
      <div class="form-item">
        <label>用户名:</label>
        <input v-model="loginData.username" type="text" />
      </div>
      
      <div class="form-item">
        <label>密码:</label>
        <input v-model="loginData.password" type="password" />
      </div>
      
      <div class="form-item">
        <label>
          <input v-model="loginData.rememberMe" type="checkbox" />
          记住我
        </label>
      </div>
      
      <div class="form-item">
        <button @click="testDirectAPI" :disabled="loading">直接测试API</button>
        <button @click="testViaStore" :disabled="loading">通过Store测试</button>
        <button @click="clearResults">清除结果</button>
      </div>
    </div>
    
    <div class="results">
      <h3>测试结果:</h3>
      <div class="result-item" v-for="(result, index) in results" :key="index" :class="result.type">
        <div class="result-time">{{ result.time }}</div>
        <div class="result-title">{{ result.title }}</div>
        <pre class="result-content">{{ result.content }}</pre>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import axios from 'axios'
import { useAuthStore } from '@/stores/auth'

// 响应式数据
const loading = ref(false)
const apiUrl = ref('http://localhost:8081/api/auth/login')
const authStore = useAuthStore()

const loginData = reactive({
  username: 'testuser002',
  password: 'Test123456',
  rememberMe: false,
  deviceInfo: getDeviceInfo(),
  ipAddress: '127.0.0.1'
})

const results = ref<Array<{
  time: string
  title: string
  content: string
  type: 'success' | 'error' | 'info'
}>>([])

// 获取设备信息
function getDeviceInfo(): string {
  return `${navigator.platform} - ${navigator.userAgent.substring(0, 100)}`
}

// 添加结果
function addResult(title: string, content: any, type: 'success' | 'error' | 'info' = 'info') {
  results.value.unshift({
    time: new Date().toLocaleTimeString(),
    title,
    content: typeof content === 'string' ? content : JSON.stringify(content, null, 2),
    type
  })
}

// 直接测试API
async function testDirectAPI() {
  loading.value = true
  addResult('开始直接API测试', `请求地址: ${apiUrl.value}\n请求数据: ${JSON.stringify(loginData, null, 2)}`)
  
  try {
    const response = await axios.post(apiUrl.value, loginData, {
      headers: {
        'Content-Type': 'application/json'
      },
      timeout: 10000
    })
    
    addResult('直接API测试成功', {
      status: response.status,
      statusText: response.statusText,
      headers: response.headers,
      data: response.data
    }, 'success')
    
  } catch (error: any) {
    addResult('直接API测试失败', {
      message: error.message,
      code: error.code,
      response: error.response ? {
        status: error.response.status,
        statusText: error.response.statusText,
        data: error.response.data
      } : null,
      config: {
        url: error.config?.url,
        method: error.config?.method,
        data: error.config?.data
      }
    }, 'error')
  } finally {
    loading.value = false
  }
}

// 通过Store测试
async function testViaStore() {
  loading.value = true
  addResult('开始Store测试', `使用认证Store进行登录测试`)
  
  try {
    const success = await authStore.login({
      username: loginData.username,
      password: loginData.password,
      rememberMe: loginData.rememberMe,
      deviceInfo: loginData.deviceInfo,
      ipAddress: loginData.ipAddress
    })
    
    if (success) {
      addResult('Store测试成功', {
        success: true,
        userInfo: authStore.userInfo,
        tokenInfo: authStore.tokenInfo
      }, 'success')
    } else {
      addResult('Store测试失败', 'login方法返回false', 'error')
    }
    
  } catch (error: any) {
    addResult('Store测试异常', {
      message: error.message,
      stack: error.stack
    }, 'error')
  } finally {
    loading.value = false
  }
}

// 清除结果
function clearResults() {
  results.value = []
}
</script>

<style scoped>
.login-test-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.test-form {
  background: #f5f5f5;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
}

.form-item {
  margin-bottom: 15px;
}

.form-item label {
  display: block;
  margin-bottom: 5px;
  font-weight: bold;
}

.form-item input[type="text"],
.form-item input[type="password"] {
  width: 100%;
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  box-sizing: border-box;
}

.form-item button {
  margin-right: 10px;
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  background: #007bff;
  color: white;
}

.form-item button:hover {
  background: #0056b3;
}

.form-item button:disabled {
  background: #ccc;
  cursor: not-allowed;
}

.results {
  margin-top: 20px;
}

.result-item {
  margin-bottom: 15px;
  padding: 15px;
  border-radius: 4px;
  border-left: 4px solid #ccc;
}

.result-item.success {
  background: #d4edda;
  border-left-color: #28a745;
}

.result-item.error {
  background: #f8d7da;
  border-left-color: #dc3545;
}

.result-item.info {
  background: #d1ecf1;
  border-left-color: #17a2b8;
}

.result-time {
  font-size: 12px;
  color: #666;
  margin-bottom: 5px;
}

.result-title {
  font-weight: bold;
  margin-bottom: 10px;
}

.result-content {
  background: #f8f9fa;
  padding: 10px;
  border-radius: 4px;
  overflow-x: auto;
  white-space: pre-wrap;
  font-family: monospace;
  font-size: 12px;
}
</style>

<template>
  <div class="login-container">
    <div class="login-content">
      <!-- 左侧背景 -->
      <div class="login-banner">
        <div class="banner-content">
          <h1 class="banner-title">VisThink ERP</h1>
          <p class="banner-subtitle">多租户电商管理系统</p>
          <div class="banner-features">
            <div class="feature-item">
              <CheckCircleOutlined />
              <span>多平台集成</span>
            </div>
            <div class="feature-item">
              <CheckCircleOutlined />
              <span>智能库存管理</span>
            </div>
            <div class="feature-item">
              <CheckCircleOutlined />
              <span>订单全流程跟踪</span>
            </div>
            <div class="feature-item">
              <CheckCircleOutlined />
              <span>数据实时同步</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧登录表单 -->
      <div class="login-form-container">
        <div class="login-form">
          <div class="form-header">
            <h2 class="form-title">用户登录</h2>
            <p class="form-subtitle">欢迎使用 VisThink ERP 管理系统</p>
          </div>

          <!-- 登录表单 -->
          <AForm
            ref="formRef"
            :model="loginForm"
            :rules="rules"
            @finish="handleLogin"
            @finishFailed="handleLoginFailed"
            layout="vertical"
          >
            <AFormItem name="username" label="用户名">
              <AInput
                v-model:value="loginForm.username"
                size="large"
                placeholder="请输入用户名"
                :prefix="h(UserOutlined)"
                :disabled="authStore.loginLoading"
              />
            </AFormItem>

            <AFormItem name="password" label="密码">
              <AInputPassword
                v-model:value="loginForm.password"
                size="large"
                placeholder="请输入密码"
                :prefix="h(LockOutlined)"
                :disabled="authStore.loginLoading"
              />
            </AFormItem>

            <AFormItem>
              <div class="form-options">
                <ACheckbox
                  v-model:checked="loginForm.remember"
                  :disabled="authStore.loginLoading"
                >
                  记住用户名
                </ACheckbox>
                <AButton
                  type="link"
                  class="forgot-password"
                  @click="handleForgotPassword"
                  :disabled="authStore.loginLoading"
                >
                  忘记密码？
                </AButton>
              </div>
            </AFormItem>

            <AFormItem>
              <AButton
                type="primary"
                html-type="submit"
                size="large"
                block
                :loading="authStore.loginLoading"
                class="login-button"
              >
                {{ authStore.loginLoading ? '登录中...' : '登录' }}
              </AButton>
            </AFormItem>
          </AForm>

          <!-- 测试用户提示 -->
          <div class="test-info" v-if="showTestInfo">
            <ADivider>测试账号</ADivider>
            <div class="test-accounts">
              <div class="test-account" @click="fillTestAccount('testuser002', 'Test123456')">
                <strong>测试用户:</strong> testuser002 / Test123456
              </div>
            </div>
          </div>

          <!-- 其他登录方式 -->
          <div class="other-login">
            <ADivider>其他登录方式</ADivider>
            <div class="social-login">
              <AButton
                shape="circle"
                size="large"
                class="social-btn"
                @click="handleSocialLogin('微信')"
                :disabled="authStore.loginLoading"
              >
                <WechatOutlined />
              </AButton>
              <AButton
                shape="circle"
                size="large"
                class="social-btn"
                @click="handleSocialLogin('支付宝')"
                :disabled="authStore.loginLoading"
              >
                <AlipayOutlined />
              </AButton>
              <AButton
                shape="circle"
                size="large"
                class="social-btn"
                @click="handleSocialLogin('GitHub')"
                :disabled="authStore.loginLoading"
              >
                <GithubOutlined />
              </AButton>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 性能监控显示 -->
    <div class="performance-info" v-if="showPerformanceInfo">
      <div class="performance-item">
        <span>API响应时间: {{ apiResponseTime }}ms</span>
      </div>
      <div class="performance-item">
        <span>登录状态: {{ loginStatus }}</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, h, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import type { FormInstance } from 'ant-design-vue'
import {
  UserOutlined,
  LockOutlined,
  CheckCircleOutlined,
  WechatOutlined,
  AlipayOutlined,
  GithubOutlined
} from '@ant-design/icons-vue'
import { useAuthStore } from '@/stores/auth'
import { StorageKeys } from '@/types/auth'

// ========== 组合式API ==========
const router = useRouter()
const authStore = useAuthStore()

// ========== 响应式数据 ==========
const formRef = ref<FormInstance>()
const apiResponseTime = ref(0)
const loginStatus = ref('未登录')

// 登录表单数据
const loginForm = reactive({
  username: 'testuser002', // 使用已验证的测试用户
  password: 'Test123456',  // 使用已验证的测试密码
  remember: false
})

// 表单验证规则
const rules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 50, message: '用户名长度为3-50个字符', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, max: 50, message: '密码长度为6-50个字符', trigger: 'blur' }
  ]
}

// ========== 计算属性 ==========
const showTestInfo = computed(() => import.meta.env.DEV)
const showPerformanceInfo = computed(() => import.meta.env.DEV)

// ========== 生命周期 ==========
onMounted(() => {
  // 恢复记住的用户名
  restoreRememberedUsername()

  // 检查是否已登录
  if (authStore.isLoggedIn) {
    router.push('/')
  }
})

// ========== 方法定义 ==========

/**
 * 恢复记住的用户名
 */
const restoreRememberedUsername = () => {
  const rememberedUsername = localStorage.getItem(StorageKeys.LAST_LOGIN_USERNAME)
  const rememberMe = localStorage.getItem(StorageKeys.REMEMBER_ME) === 'true'

  if (rememberMe && rememberedUsername) {
    loginForm.username = rememberedUsername
    loginForm.remember = true
  }
}

/**
 * 获取客户端IP地址
 */
const getClientIP = async (): Promise<string> => {
  try {
    // 在实际生产环境中，可以通过后端API获取真实IP
    // 这里使用默认值，后端会自动获取真实IP
    return '127.0.0.1'
  } catch {
    return '127.0.0.1'
  }
}

/**
 * 获取设备信息
 */
const getDeviceInfo = (): string => {
  const userAgent = navigator.userAgent
  const platform = navigator.platform
  return `${platform} - ${userAgent.substring(0, 100)}`
}

/**
 * 登录处理
 */
const handleLogin = async (values: any) => {
  const startTime = Date.now()
  loginStatus.value = '登录中...'

  try {
    // 获取设备信息和IP地址
    const deviceInfo = getDeviceInfo()
    const ipAddress = await getClientIP()

    // 调用认证Store的登录方法
    const success = await authStore.login({
      username: values.username,
      password: values.password,
      rememberMe: values.remember || false,
      deviceInfo,
      ipAddress
    })

    // 计算API响应时间
    apiResponseTime.value = Date.now() - startTime

    if (success) {
      loginStatus.value = '登录成功'

      // 保存记住用户名的设置
      handleRememberMe(values)

      // 获取用户信息
      await authStore.fetchUserInfo()

      // 跳转到首页或之前访问的页面
      const redirect = router.currentRoute.value.query.redirect as string
      await router.push(redirect || '/')

      // 显示欢迎信息
      message.success(`欢迎回来，${authStore.userDisplayName}！`)
    } else {
      loginStatus.value = '登录失败'
    }
  } catch (error) {
    console.error('登录处理失败:', error)
    loginStatus.value = '登录异常'
    apiResponseTime.value = Date.now() - startTime
  }
}

/**
 * 处理记住用户名
 */
const handleRememberMe = (values: any) => {
  if (values.remember) {
    localStorage.setItem(StorageKeys.REMEMBER_ME, 'true')
    localStorage.setItem(StorageKeys.LAST_LOGIN_USERNAME, values.username)
  } else {
    localStorage.removeItem(StorageKeys.REMEMBER_ME)
    localStorage.removeItem(StorageKeys.LAST_LOGIN_USERNAME)
  }
}

/**
 * 登录失败处理
 */
const handleLoginFailed = (errorInfo: any) => {
  console.log('登录表单验证失败:', errorInfo)
  message.error('请检查输入信息')
  loginStatus.value = '表单验证失败'
}

/**
 * 填充测试账号
 */
const fillTestAccount = (username: string, password: string) => {
  loginForm.username = username
  loginForm.password = password
  message.info('已填充测试账号信息')
}

/**
 * 社交登录处理（预留功能）
 */
const handleSocialLogin = (type: string) => {
  message.info(`${type}登录功能开发中...`)
}

/**
 * 忘记密码处理
 */
const handleForgotPassword = () => {
  message.info('忘记密码功能开发中...')
}
</script>

<style lang="less" scoped>
.login-container {
  width: 100vw;
  height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  position: relative;
}

.login-content {
  width: 1000px;
  height: 600px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  display: flex;
  overflow: hidden;
}

.login-banner {
  flex: 1;
  background: linear-gradient(135deg, #1890ff 0%, #722ed1 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  position: relative;

  .banner-content {
    text-align: center;
    z-index: 1;
    position: relative;

    .banner-title {
      font-size: 48px;
      font-weight: bold;
      margin-bottom: 16px;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    }

    .banner-subtitle {
      font-size: 18px;
      margin-bottom: 40px;
      opacity: 0.9;
    }

    .banner-features {
      .feature-item {
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 16px;
        font-size: 16px;

        .anticon {
          margin-right: 12px;
          color: #52c41a;
        }
      }
    }
  }
}

.login-form-container {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px;
}

.login-form {
  width: 100%;
  max-width: 400px;

  .form-header {
    text-align: center;
    margin-bottom: 40px;

    .form-title {
      font-size: 28px;
      font-weight: bold;
      color: #262626;
      margin-bottom: 8px;
    }

    .form-subtitle {
      color: #8c8c8c;
      font-size: 14px;
    }
  }

  .form-options {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .forgot-password {
      padding: 0;
      font-size: 14px;
    }
  }

  .login-button {
    height: 48px;
    font-size: 16px;
    font-weight: 500;
    margin-top: 16px;
  }

  .test-info {
    margin-top: 20px;

    .test-accounts {
      .test-account {
        padding: 8px 12px;
        background: #f0f2f5;
        border-radius: 6px;
        cursor: pointer;
        font-size: 12px;
        transition: all 0.3s;

        &:hover {
          background: #e6f7ff;
          color: #1890ff;
        }
      }
    }
  }

  .other-login {
    margin-top: 32px;

    .social-login {
      display: flex;
      justify-content: center;
      gap: 16px;

      .social-btn {
        width: 40px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        border: 1px solid #d9d9d9;
        color: #8c8c8c;
        transition: all 0.3s;

        &:hover:not(:disabled) {
          color: #1890ff;
          border-color: #1890ff;
        }

        &:disabled {
          opacity: 0.5;
          cursor: not-allowed;
        }
      }
    }
  }
}

.performance-info {
  position: absolute;
  top: 20px;
  right: 20px;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 10px;
  border-radius: 6px;
  font-size: 12px;

  .performance-item {
    margin-bottom: 4px;

    &:last-child {
      margin-bottom: 0;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .login-content {
    width: 90vw;
    height: 90vh;
    flex-direction: column;
  }

  .login-banner {
    flex: none;
    height: 200px;

    .banner-content {
      .banner-title {
        font-size: 32px;
      }

      .banner-subtitle {
        font-size: 14px;
        margin-bottom: 20px;
      }

      .banner-features {
        display: none;
      }
    }
  }

  .login-form-container {
    padding: 20px;
  }

  .performance-info {
    position: relative;
    top: auto;
    right: auto;
    margin-top: 20px;
  }
}
</style>

<!--
  用户管理主页面
  功能：用户列表查询、创建、编辑、删除、批量操作
-->
<template>
  <div class="user-management">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>用户管理</h2>
      <p>管理系统用户账户、角色权限和基本信息</p>
    </div>

    <!-- 搜索表单 -->
    <a-card class="search-card" :bordered="false">
      <a-form
        ref="searchFormRef"
        :model="searchForm"
        layout="inline"
        @finish="handleSearch"
      >
        <a-form-item label="关键词" name="keyword">
          <a-input
            v-model:value="searchForm.keyword"
            placeholder="用户名、邮箱、姓名"
            allow-clear
            style="width: 200px"
          />
        </a-form-item>
        
        <a-form-item label="用户状态" name="status">
          <a-select
            v-model:value="searchForm.status"
            placeholder="请选择状态"
            allow-clear
            style="width: 120px"
          >
            <a-select-option
              v-for="option in USER_STATUS_OPTIONS"
              :key="option.value"
              :value="option.value"
            >
              <a-tag :color="option.color">{{ option.label }}</a-tag>
            </a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item label="用户类型" name="userType">
          <a-select
            v-model:value="searchForm.userType"
            placeholder="请选择类型"
            allow-clear
            style="width: 120px"
          >
            <a-select-option
              v-for="option in USER_TYPE_OPTIONS"
              :key="option.value"
              :value="option.value"
            >
              <a-tag :color="option.color">{{ option.label }}</a-tag>
            </a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item>
          <a-space>
            <a-button type="primary" html-type="submit" :loading="loading">
              <template #icon><SearchOutlined /></template>
              搜索
            </a-button>
            <a-button @click="handleReset">
              <template #icon><ReloadOutlined /></template>
              重置
            </a-button>
          </a-space>
        </a-form-item>
      </a-form>
    </a-card>

    <!-- 操作工具栏 -->
    <a-card class="toolbar-card" :bordered="false">
      <div class="toolbar">
        <div class="toolbar-left">
          <a-space>
            <a-button type="primary" @click="handleCreate">
              <template #icon><PlusOutlined /></template>
              新建用户
            </a-button>
            
            <a-dropdown :disabled="!hasSelected">
              <a-button>
                <template #icon><DownOutlined /></template>
                批量操作
              </a-button>
              <template #overlay>
                <a-menu @click="handleBatchAction">
                  <a-menu-item key="enable">
                    <CheckCircleOutlined />
                    启用
                  </a-menu-item>
                  <a-menu-item key="disable">
                    <StopOutlined />
                    禁用
                  </a-menu-item>
                  <a-menu-item key="lock">
                    <LockOutlined />
                    锁定
                  </a-menu-item>
                  <a-menu-item key="unlock">
                    <UnlockOutlined />
                    解锁
                  </a-menu-item>
                  <a-menu-divider />
                  <a-menu-item key="delete" class="danger-item">
                    <DeleteOutlined />
                    删除
                  </a-menu-item>
                </a-menu>
              </template>
            </a-dropdown>

            <a-button @click="handleImport">
              <template #icon><ImportOutlined /></template>
              导入
            </a-button>
            
            <a-button @click="handleExport">
              <template #icon><ExportOutlined /></template>
              导出
            </a-button>
          </a-space>
        </div>

        <div class="toolbar-right">
          <a-space>
            <a-tooltip title="刷新">
              <a-button @click="handleRefresh">
                <template #icon><ReloadOutlined /></template>
              </a-button>
            </a-tooltip>
            
            <a-tooltip title="列设置">
              <a-button @click="showColumnSetting = true">
                <template #icon><SettingOutlined /></template>
              </a-button>
            </a-tooltip>
          </a-space>
        </div>
      </div>

      <!-- 选中提示 -->
      <div v-if="hasSelected" class="selection-info">
        <a-alert
          :message="`已选择 ${selectedRowKeys.length} 项`"
          type="info"
          show-icon
          closable
          @close="clearSelection"
        />
      </div>
    </a-card>

    <!-- 用户列表表格 -->
    <a-card :bordered="false">
      <a-table
        ref="tableRef"
        :columns="columns"
        :data-source="dataSource"
        :loading="loading"
        :pagination="pagination"
        :row-selection="rowSelection"
        :scroll="{ x: 1500 }"
        row-key="id"
        @change="handleTableChange"
      >
        <!-- 头像列 -->
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'avatar'">
            <a-avatar
              :src="record.avatarUrl"
              :alt="record.displayName"
              size="small"
            >
              {{ record.displayName?.charAt(0) }}
            </a-avatar>
          </template>

          <!-- 用户信息列 -->
          <template v-else-if="column.key === 'userInfo'">
            <div class="user-info">
              <div class="username">{{ record.username }}</div>
              <div class="real-name">{{ record.realName || record.nickname }}</div>
              <div class="email">{{ record.email }}</div>
            </div>
          </template>

          <!-- 状态列 -->
          <template v-else-if="column.key === 'status'">
            <a-tag :color="getStatusColor(record.accountStatus)">
              {{ getStatusText(record.accountStatus) }}
            </a-tag>
          </template>

          <!-- 用户类型列 -->
          <template v-else-if="column.key === 'userType'">
            <a-tag :color="getUserTypeColor(record.userType)">
              {{ getUserTypeText(record.userType) }}
            </a-tag>
          </template>

          <!-- 最后登录列 -->
          <template v-else-if="column.key === 'lastLogin'">
            <div v-if="record.lastLoginTime">
              <div>{{ formatDateTime(record.lastLoginTime) }}</div>
              <div class="text-gray">{{ record.lastLoginIp }}</div>
            </div>
            <span v-else class="text-gray">从未登录</span>
          </template>

          <!-- 操作列 -->
          <template v-else-if="column.key === 'actions'">
            <a-space>
              <a-tooltip title="查看详情">
                <a-button type="text" size="small" @click="handleView(record)">
                  <template #icon><EyeOutlined /></template>
                </a-button>
              </a-tooltip>
              
              <a-tooltip title="编辑">
                <a-button type="text" size="small" @click="handleEdit(record)">
                  <template #icon><EditOutlined /></template>
                </a-button>
              </a-tooltip>
              
              <a-dropdown>
                <a-button type="text" size="small">
                  <template #icon><MoreOutlined /></template>
                </a-button>
                <template #overlay>
                  <a-menu @click="({ key }) => handleAction(key, record)">
                    <a-menu-item key="resetPassword">
                      <KeyOutlined />
                      重置密码
                    </a-menu-item>
                    <a-menu-item key="assignRole">
                      <UserOutlined />
                      分配角色
                    </a-menu-item>
                    <a-menu-item
                      :key="record.accountStatus === 1 ? 'disable' : 'enable'"
                    >
                      <template v-if="record.accountStatus === 1">
                        <StopOutlined />
                        禁用
                      </template>
                      <template v-else>
                        <CheckCircleOutlined />
                        启用
                      </template>
                    </a-menu-item>
                    <a-menu-divider />
                    <a-menu-item key="delete" class="danger-item">
                      <DeleteOutlined />
                      删除
                    </a-menu-item>
                  </a-menu>
                </template>
              </a-dropdown>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 用户表单弹窗 -->
    <UserFormModal
      v-model:visible="formModalVisible"
      :mode="formMode"
      :user-data="currentUser"
      @success="handleFormSuccess"
    />

    <!-- 用户详情弹窗 -->
    <UserDetailModal
      v-model:visible="detailModalVisible"
      :user-data="currentUser"
    />

    <!-- 角色分配弹窗 -->
    <UserRoleModal
      v-model:visible="roleModalVisible"
      :user-data="currentUser"
      @success="handleRoleSuccess"
    />

    <!-- 密码重置弹窗 -->
    <UserPasswordModal
      v-model:visible="passwordModalVisible"
      :user-data="currentUser"
      @success="handlePasswordSuccess"
    />

    <!-- 列设置弹窗 -->
    <ColumnSettingModal
      v-model:visible="showColumnSetting"
      :columns="allColumns"
      @change="handleColumnChange"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { message, Modal } from 'ant-design-vue'
import {
  SearchOutlined,
  ReloadOutlined,
  PlusOutlined,
  DownOutlined,
  CheckCircleOutlined,
  StopOutlined,
  LockOutlined,
  UnlockOutlined,
  DeleteOutlined,
  ImportOutlined,
  ExportOutlined,
  SettingOutlined,
  EyeOutlined,
  EditOutlined,
  MoreOutlined,
  KeyOutlined,
  UserOutlined
} from '@ant-design/icons-vue'

import UserApi from '@/api/user'
import { formatDateTime } from '@/utils/date'
import {
  USER_STATUS_OPTIONS,
  USER_TYPE_OPTIONS,
  UserStatus,
  UserType,
  type UserAccount,
  type UserQueryParams
} from '@/types/user'

// 导入子组件
import UserFormModal from './components/UserFormModal.vue'
import UserDetailModal from './components/UserDetailModal.vue'
import UserRoleModal from './components/UserRoleModal.vue'
import UserPasswordModal from './components/UserPasswordModal.vue'
import ColumnSettingModal from './components/ColumnSettingModal.vue'

// 响应式数据
const loading = ref(false)
const dataSource = ref<UserAccount[]>([])
const selectedRowKeys = ref<number[]>([])
const selectedRows = ref<UserAccount[]>([])

// 搜索表单
const searchFormRef = ref()
const searchForm = reactive<UserQueryParams>({
  keyword: '',
  status: undefined,
  userType: undefined,
  page: 1,
  size: 10
})

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number, range: [number, number]) =>
    `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
})

// 弹窗状态
const formModalVisible = ref(false)
const detailModalVisible = ref(false)
const roleModalVisible = ref(false)
const passwordModalVisible = ref(false)
const showColumnSetting = ref(false)

// 表单模式和当前用户
const formMode = ref<'create' | 'edit'>('create')
const currentUser = ref<UserAccount | null>(null)

// 计算属性
const hasSelected = computed(() => selectedRowKeys.value.length > 0)

// 表格行选择配置
const rowSelection = computed(() => ({
  selectedRowKeys: selectedRowKeys.value,
  onChange: (keys: number[], rows: UserAccount[]) => {
    selectedRowKeys.value = keys
    selectedRows.value = rows
  },
  onSelectAll: (selected: boolean, selectedRows: UserAccount[], changeRows: UserAccount[]) => {
    console.log('onSelectAll', selected, selectedRows, changeRows)
  }
}))

// 表格列配置
const allColumns = [
  {
    title: '头像',
    key: 'avatar',
    dataIndex: 'avatarUrl',
    width: 80,
    fixed: 'left'
  },
  {
    title: '用户信息',
    key: 'userInfo',
    dataIndex: 'username',
    width: 200,
    fixed: 'left'
  },
  {
    title: '状态',
    key: 'status',
    dataIndex: 'accountStatus',
    width: 100,
    filters: USER_STATUS_OPTIONS.map(item => ({
      text: item.label,
      value: item.value
    }))
  },
  {
    title: '用户类型',
    key: 'userType',
    dataIndex: 'userType',
    width: 120,
    filters: USER_TYPE_OPTIONS.map(item => ({
      text: item.label,
      value: item.value
    }))
  },
  {
    title: '手机号',
    key: 'phoneNumber',
    dataIndex: 'phoneNumber',
    width: 120
  },
  {
    title: '部门职位',
    key: 'department',
    dataIndex: 'position',
    width: 150
  },
  {
    title: '最后登录',
    key: 'lastLogin',
    dataIndex: 'lastLoginTime',
    width: 180,
    sorter: true
  },
  {
    title: '创建时间',
    key: 'createTime',
    dataIndex: 'createTime',
    width: 180,
    sorter: true
  },
  {
    title: '操作',
    key: 'actions',
    fixed: 'right',
    width: 120
  }
]

const columns = ref([...allColumns])

// 页面初始化
onMounted(() => {
  loadUsers()
})

// 加载用户列表
const loadUsers = async () => {
  try {
    loading.value = true
    const params = {
      ...searchForm,
      page: pagination.current,
      size: pagination.pageSize
    }
    
    const response = await UserApi.getUsers(params)
    
    if (response.success) {
      dataSource.value = response.data.records
      pagination.total = response.data.total
      pagination.current = response.data.pageNum
      pagination.pageSize = response.data.pageSize
    } else {
      message.error(response.message || '加载用户列表失败')
    }
  } catch (error) {
    console.error('加载用户列表失败:', error)
    message.error('加载用户列表失败')
  } finally {
    loading.value = false
  }
}

// 搜索处理
const handleSearch = () => {
  pagination.current = 1
  loadUsers()
}

// 重置搜索
const handleReset = () => {
  searchFormRef.value?.resetFields()
  Object.assign(searchForm, {
    keyword: '',
    status: undefined,
    userType: undefined,
    page: 1,
    size: 10
  })
  pagination.current = 1
  loadUsers()
}

// 表格变化处理
const handleTableChange = (pag: any, filters: any, sorter: any) => {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
  
  // 处理筛选
  if (filters.accountStatus) {
    searchForm.status = filters.accountStatus[0]
  }
  if (filters.userType) {
    searchForm.userType = filters.userType[0]
  }
  
  loadUsers()
}

// 刷新
const handleRefresh = () => {
  loadUsers()
}

// 清除选择
const clearSelection = () => {
  selectedRowKeys.value = []
  selectedRows.value = []
}

// 创建用户
const handleCreate = () => {
  formMode.value = 'create'
  currentUser.value = null
  formModalVisible.value = true
}

// 编辑用户
const handleEdit = (record: UserAccount) => {
  formMode.value = 'edit'
  currentUser.value = record
  formModalVisible.value = true
}

// 查看详情
const handleView = (record: UserAccount) => {
  currentUser.value = record
  detailModalVisible.value = true
}

// 表单成功回调
const handleFormSuccess = () => {
  formModalVisible.value = false
  loadUsers()
  message.success(formMode.value === 'create' ? '创建成功' : '更新成功')
}

// 角色分配成功回调
const handleRoleSuccess = () => {
  roleModalVisible.value = false
  loadUsers()
  message.success('角色分配成功')
}

// 密码重置成功回调
const handlePasswordSuccess = () => {
  passwordModalVisible.value = false
  message.success('密码重置成功')
}

// 操作处理
const handleAction = async (action: string, record: UserAccount) => {
  switch (action) {
    case 'resetPassword':
      currentUser.value = record
      passwordModalVisible.value = true
      break
    case 'assignRole':
      currentUser.value = record
      roleModalVisible.value = true
      break
    case 'enable':
      await handleStatusChange(record.id!, 'enable')
      break
    case 'disable':
      await handleStatusChange(record.id!, 'disable')
      break
    case 'delete':
      handleDelete(record)
      break
  }
}

// 状态变更
const handleStatusChange = async (id: number, action: string) => {
  try {
    loading.value = true
    
    switch (action) {
      case 'enable':
        await UserApi.enableUser(id)
        message.success('启用成功')
        break
      case 'disable':
        await UserApi.disableUser(id)
        message.success('禁用成功')
        break
      case 'lock':
        await UserApi.lockUser(id)
        message.success('锁定成功')
        break
      case 'unlock':
        await UserApi.unlockUser(id)
        message.success('解锁成功')
        break
    }
    
    loadUsers()
  } catch (error) {
    console.error('状态变更失败:', error)
    message.error('操作失败')
  } finally {
    loading.value = false
  }
}

// 删除用户
const handleDelete = (record: UserAccount) => {
  Modal.confirm({
    title: '确认删除',
    content: `确定要删除用户 "${record.username}" 吗？此操作不可恢复。`,
    okText: '确定',
    cancelText: '取消',
    okType: 'danger',
    onOk: async () => {
      try {
        await UserApi.deleteUser(record.id!)
        message.success('删除成功')
        loadUsers()
      } catch (error) {
        console.error('删除失败:', error)
        message.error('删除失败')
      }
    }
  })
}

// 批量操作
const handleBatchAction = ({ key }: { key: string }) => {
  if (!hasSelected.value) {
    message.warning('请先选择要操作的用户')
    return
  }

  const actionMap = {
    enable: '启用',
    disable: '禁用',
    lock: '锁定',
    unlock: '解锁',
    delete: '删除'
  }

  const actionText = actionMap[key as keyof typeof actionMap]
  
  Modal.confirm({
    title: `批量${actionText}`,
    content: `确定要${actionText} ${selectedRowKeys.value.length} 个用户吗？`,
    okText: '确定',
    cancelText: '取消',
    okType: key === 'delete' ? 'danger' : 'primary',
    onOk: async () => {
      try {
        loading.value = true
        
        await UserApi.batchOperation({
          userIds: selectedRowKeys.value,
          operation: key as any
        })
        
        message.success(`批量${actionText}成功`)
        clearSelection()
        loadUsers()
      } catch (error) {
        console.error(`批量${actionText}失败:`, error)
        message.error(`批量${actionText}失败`)
      } finally {
        loading.value = false
      }
    }
  })
}

// 导入用户
const handleImport = () => {
  message.info('导入功能开发中...')
}

// 导出用户
const handleExport = () => {
  message.info('导出功能开发中...')
}

// 列设置变更
const handleColumnChange = (newColumns: any[]) => {
  columns.value = newColumns
}

// 工具函数
const getStatusColor = (status: number) => {
  const option = USER_STATUS_OPTIONS.find(item => item.value === status)
  return option?.color || 'default'
}

const getStatusText = (status: number) => {
  const option = USER_STATUS_OPTIONS.find(item => item.value === status)
  return option?.label || '未知'
}

const getUserTypeColor = (type: number) => {
  const option = USER_TYPE_OPTIONS.find(item => item.value === type)
  return option?.color || 'default'
}

const getUserTypeText = (type: number) => {
  const option = USER_TYPE_OPTIONS.find(item => item.value === type)
  return option?.label || '未知'
}
</script>

<style scoped lang="less">
.user-management {
  .page-header {
    margin-bottom: 16px;
    
    h2 {
      margin: 0 0 8px 0;
      font-size: 20px;
      font-weight: 600;
    }
    
    p {
      margin: 0;
      color: #666;
      font-size: 14px;
    }
  }

  .search-card,
  .toolbar-card {
    margin-bottom: 16px;
  }

  .toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
  }

  .selection-info {
    margin-top: 16px;
  }

  .user-info {
    .username {
      font-weight: 500;
      color: #1890ff;
    }
    
    .real-name {
      font-size: 12px;
      color: #666;
    }
    
    .email {
      font-size: 12px;
      color: #999;
    }
  }

  .text-gray {
    color: #999;
    font-size: 12px;
  }

  .danger-item {
    color: #ff4d4f !important;
  }
}
</style>

<!--
  用户管理主页面
  功能：用户列表查询、创建、编辑、删除、批量操作
-->
<template>
  <div class="user-management">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>用户管理</h2>
      <p>管理系统用户账户、角色权限和基本信息</p>
    </div>

    <!-- 搜索表单 -->
    <a-card class="search-card" :bordered="false">
      <a-form
        ref="searchFormRef"
        :model="searchForm"
        layout="inline"
        @finish="handleSearch"
      >
        <a-form-item label="关键词" name="keyword">
          <a-input
            v-model:value="searchForm.keyword"
            placeholder="用户名、邮箱、姓名"
            allow-clear
            style="width: 200px"
          />
        </a-form-item>
        
        <a-form-item label="用户状态" name="status">
          <a-select
            v-model:value="searchForm.status"
            placeholder="请选择状态"
            allow-clear
            style="width: 120px"
          >
            <a-select-option
              v-for="option in USER_STATUS_OPTIONS"
              :key="option.value"
              :value="option.value"
            >
              <a-tag :color="option.color">{{ option.label }}</a-tag>
            </a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item>
          <a-space>
            <a-button type="primary" html-type="submit" :loading="loading">
              <template #icon><SearchOutlined /></template>
              搜索
            </a-button>
            <a-button @click="handleReset">
              <template #icon><ReloadOutlined /></template>
              重置
            </a-button>
          </a-space>
        </a-form-item>
      </a-form>
    </a-card>

    <!-- 操作工具栏 -->
    <a-card class="toolbar-card" :bordered="false">
      <div class="toolbar">
        <div class="toolbar-left">
          <a-space>
            <a-button type="primary" @click="handleCreate">
              <template #icon><PlusOutlined /></template>
              新建用户
            </a-button>
            
            <a-button @click="handleRefresh">
              <template #icon><ReloadOutlined /></template>
              刷新
            </a-button>
          </a-space>
        </div>
      </div>
    </a-card>

    <!-- 用户列表表格 -->
    <a-card :bordered="false">
      <a-table
        ref="tableRef"
        :columns="columns"
        :data-source="dataSource"
        :loading="loading"
        :pagination="pagination"
        :scroll="{ x: 1200 }"
        row-key="id"
        @change="handleTableChange"
      >
        <!-- 用户信息列 -->
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'userInfo'">
            <div class="user-info">
              <div class="username">{{ record.username }}</div>
              <div class="real-name">{{ record.realName || record.nickname }}</div>
              <div class="email">{{ record.email }}</div>
            </div>
          </template>

          <!-- 状态列 -->
          <template v-else-if="column.key === 'status'">
            <a-tag :color="getStatusColor(record.accountStatus)">
              {{ getStatusText(record.accountStatus) }}
            </a-tag>
          </template>

          <!-- 用户类型列 -->
          <template v-else-if="column.key === 'userType'">
            <a-tag :color="getUserTypeColor(record.userType)">
              {{ getUserTypeText(record.userType) }}
            </a-tag>
          </template>

          <!-- 最后登录列 -->
          <template v-else-if="column.key === 'lastLogin'">
            <div v-if="record.lastLoginTime">
              <div>{{ formatDateTime(record.lastLoginTime) }}</div>
              <div class="text-gray">{{ record.lastLoginIp }}</div>
            </div>
            <span v-else class="text-gray">从未登录</span>
          </template>

          <!-- 操作列 -->
          <template v-else-if="column.key === 'actions'">
            <a-space>
              <a-tooltip title="编辑">
                <a-button type="text" size="small" @click="handleEdit(record)">
                  <template #icon><EditOutlined /></template>
                </a-button>
              </a-tooltip>
              
              <a-tooltip title="删除">
                <a-button type="text" size="small" danger @click="handleDelete(record)">
                  <template #icon><DeleteOutlined /></template>
                </a-button>
              </a-tooltip>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 用户表单弹窗 -->
    <a-modal
      v-model:open="formModalVisible"
      :title="formMode === 'create' ? '新建用户' : '编辑用户'"
      width="600px"
      @ok="handleFormSubmit"
      @cancel="handleFormCancel"
    >
      <a-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        layout="vertical"
      >
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="用户名" name="username">
              <a-input v-model:value="formData.username" placeholder="请输入用户名" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="邮箱" name="email">
              <a-input v-model:value="formData.email" placeholder="请输入邮箱" />
            </a-form-item>
          </a-col>
        </a-row>
        
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="真实姓名" name="realName">
              <a-input v-model:value="formData.realName" placeholder="请输入真实姓名" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="手机号" name="phoneNumber">
              <a-input v-model:value="formData.phoneNumber" placeholder="请输入手机号" />
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16" v-if="formMode === 'create'">
          <a-col :span="12">
            <a-form-item label="密码" name="password">
              <a-input-password v-model:value="formData.password" placeholder="请输入密码" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="确认密码" name="confirmPassword">
              <a-input-password v-model:value="formData.confirmPassword" placeholder="请确认密码" />
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="用户类型" name="userType">
              <a-select v-model:value="formData.userType" placeholder="请选择用户类型">
                <a-select-option
                  v-for="option in USER_TYPE_OPTIONS"
                  :key="option.value"
                  :value="option.value"
                >
                  {{ option.label }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="状态" name="accountStatus">
              <a-select v-model:value="formData.accountStatus" placeholder="请选择状态">
                <a-select-option
                  v-for="option in USER_STATUS_OPTIONS"
                  :key="option.value"
                  :value="option.value"
                >
                  {{ option.label }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { message, Modal } from 'ant-design-vue'
import {
  SearchOutlined,
  ReloadOutlined,
  PlusOutlined,
  EditOutlined,
  DeleteOutlined
} from '@ant-design/icons-vue'

import UserApi from '@/api/user'
import { formatDateTime } from '@/utils/date'
import {
  USER_STATUS_OPTIONS,
  USER_TYPE_OPTIONS,
  UserStatus,
  UserType,
  type UserAccount,
  type UserQueryParams,
  type UserCreateRequest,
  type UserUpdateRequest
} from '@/types/user'

// 响应式数据
const loading = ref(false)
const dataSource = ref<UserAccount[]>([])

// 搜索表单
const searchFormRef = ref()
const searchForm = reactive<UserQueryParams>({
  keyword: '',
  status: undefined,
  userType: undefined,
  page: 1,
  size: 10
})

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number, range: [number, number]) =>
    `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
})

// 表单相关
const formModalVisible = ref(false)
const formMode = ref<'create' | 'edit'>('create')
const formRef = ref()
const formData = reactive<UserCreateRequest & UserUpdateRequest>({
  username: '',
  email: '',
  password: '',
  confirmPassword: '',
  realName: '',
  phoneNumber: '',
  userType: UserType.REGULAR,
  accountStatus: UserStatus.ACTIVE
})

// 表单验证规则
const formRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '用户名长度为3-20个字符', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, max: 20, message: '密码长度为6-20个字符', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请确认密码', trigger: 'blur' },
    {
      validator: (rule: any, value: string) => {
        if (value && value !== formData.password) {
          return Promise.reject('两次输入的密码不一致')
        }
        return Promise.resolve()
      },
      trigger: 'blur'
    }
  ],
  realName: [
    { required: true, message: '请输入真实姓名', trigger: 'blur' }
  ],
  userType: [
    { required: true, message: '请选择用户类型', trigger: 'change' }
  ],
  accountStatus: [
    { required: true, message: '请选择状态', trigger: 'change' }
  ]
}

// 表格列配置
const columns = [
  {
    title: '用户信息',
    key: 'userInfo',
    dataIndex: 'username',
    width: 200,
    fixed: 'left'
  },
  {
    title: '状态',
    key: 'status',
    dataIndex: 'accountStatus',
    width: 100
  },
  {
    title: '用户类型',
    key: 'userType',
    dataIndex: 'userType',
    width: 120
  },
  {
    title: '手机号',
    key: 'phoneNumber',
    dataIndex: 'phoneNumber',
    width: 120
  },
  {
    title: '最后登录',
    key: 'lastLogin',
    dataIndex: 'lastLoginTime',
    width: 180
  },
  {
    title: '创建时间',
    key: 'createTime',
    dataIndex: 'createTime',
    width: 180
  },
  {
    title: '操作',
    key: 'actions',
    fixed: 'right',
    width: 120
  }
]

// 页面初始化
onMounted(() => {
  loadUsers()
})

// 加载用户列表
const loadUsers = async () => {
  try {
    loading.value = true
    const params = {
      ...searchForm,
      page: pagination.current,
      size: pagination.pageSize
    }
    
    const response = await UserApi.getUsers(params)
    
    if (response.success) {
      dataSource.value = response.data.records
      pagination.total = response.data.total
      pagination.current = response.data.pageNum
      pagination.pageSize = response.data.pageSize
    } else {
      message.error(response.message || '加载用户列表失败')
    }
  } catch (error) {
    console.error('加载用户列表失败:', error)
    message.error('加载用户列表失败')
  } finally {
    loading.value = false
  }
}

// 搜索处理
const handleSearch = () => {
  pagination.current = 1
  loadUsers()
}

// 重置搜索
const handleReset = () => {
  searchFormRef.value?.resetFields()
  Object.assign(searchForm, {
    keyword: '',
    status: undefined,
    userType: undefined,
    page: 1,
    size: 10
  })
  pagination.current = 1
  loadUsers()
}

// 表格变化处理
const handleTableChange = (pag: any) => {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
  loadUsers()
}

// 刷新
const handleRefresh = () => {
  loadUsers()
}

// 创建用户
const handleCreate = () => {
  formMode.value = 'create'
  resetFormData()
  formModalVisible.value = true
}

// 编辑用户
const handleEdit = (record: UserAccount) => {
  formMode.value = 'edit'
  Object.assign(formData, {
    id: record.id,
    username: record.username,
    email: record.email,
    realName: record.realName,
    phoneNumber: record.phoneNumber,
    userType: record.userType,
    accountStatus: record.accountStatus
  })
  formModalVisible.value = true
}

// 删除用户
const handleDelete = (record: UserAccount) => {
  Modal.confirm({
    title: '确认删除',
    content: `确定要删除用户 "${record.username}" 吗？此操作不可恢复。`,
    okText: '确定',
    cancelText: '取消',
    okType: 'danger',
    onOk: async () => {
      try {
        await UserApi.deleteUser(record.id!)
        message.success('删除成功')
        loadUsers()
      } catch (error) {
        console.error('删除失败:', error)
        message.error('删除失败')
      }
    }
  })
}

// 表单提交
const handleFormSubmit = async () => {
  try {
    await formRef.value?.validate()
    
    if (formMode.value === 'create') {
      await UserApi.createUser(formData as UserCreateRequest)
      message.success('创建成功')
    } else {
      await UserApi.updateUser(formData.id!, formData as UserUpdateRequest)
      message.success('更新成功')
    }
    
    formModalVisible.value = false
    loadUsers()
  } catch (error) {
    console.error('操作失败:', error)
    message.error('操作失败')
  }
}

// 表单取消
const handleFormCancel = () => {
  formModalVisible.value = false
  resetFormData()
}

// 重置表单数据
const resetFormData = () => {
  Object.assign(formData, {
    id: undefined,
    username: '',
    email: '',
    password: '',
    confirmPassword: '',
    realName: '',
    phoneNumber: '',
    userType: UserType.REGULAR,
    accountStatus: UserStatus.ACTIVE
  })
  formRef.value?.resetFields()
}

// 工具函数
const getStatusColor = (status: number) => {
  const option = USER_STATUS_OPTIONS.find(item => item.value === status)
  return option?.color || 'default'
}

const getStatusText = (status: number) => {
  const option = USER_STATUS_OPTIONS.find(item => item.value === status)
  return option?.label || '未知'
}

const getUserTypeColor = (type: number) => {
  const option = USER_TYPE_OPTIONS.find(item => item.value === type)
  return option?.color || 'default'
}

const getUserTypeText = (type: number) => {
  const option = USER_TYPE_OPTIONS.find(item => item.value === type)
  return option?.label || '未知'
}
</script>

<style scoped lang="less">
.user-management {
  .page-header {
    margin-bottom: 16px;
    
    h2 {
      margin: 0 0 8px 0;
      font-size: 20px;
      font-weight: 600;
    }
    
    p {
      margin: 0;
      color: #666;
      font-size: 14px;
    }
  }

  .search-card,
  .toolbar-card {
    margin-bottom: 16px;
  }

  .toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .user-info {
    .username {
      font-weight: 500;
      color: #1890ff;
    }
    
    .real-name {
      font-size: 12px;
      color: #666;
    }
    
    .email {
      font-size: 12px;
      color: #999;
    }
  }

  .text-gray {
    color: #999;
    font-size: 12px;
  }
}
</style>

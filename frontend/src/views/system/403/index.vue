<template>
  <div class="error-page">
    <div class="error-content">
      <div class="error-icon">
        <ExclamationCircleOutlined />
      </div>
      <h1 class="error-title">403</h1>
      <p class="error-description">抱歉，您没有权限访问该页面</p>
      <div class="error-actions">
        <AButton type="primary" @click="goBack">
          返回上一页
        </AButton>
        <AButton @click="goHome">
          回到首页
        </AButton>
        <AButton @click="contactAdmin">
          联系管理员
        </AButton>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import { ExclamationCircleOutlined } from '@ant-design/icons-vue'

const router = useRouter()

const goBack = () => {
  router.go(-1)
}

const goHome = () => {
  router.push('/')
}

const contactAdmin = () => {
  message.info('请联系系统管理员获取相应权限')
}
</script>

<style lang="less" scoped>
.error-page {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  background: #f0f2f5;
}

.error-content {
  text-align: center;
  padding: 40px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.error-icon {
  font-size: 72px;
  color: #faad14;
  margin-bottom: 24px;
}

.error-title {
  font-size: 48px;
  font-weight: bold;
  color: #262626;
  margin-bottom: 16px;
}

.error-description {
  font-size: 16px;
  color: #8c8c8c;
  margin-bottom: 32px;
}

.error-actions {
  display: flex;
  gap: 16px;
  justify-content: center;
}
</style>

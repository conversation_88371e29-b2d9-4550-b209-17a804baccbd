<!--
  菜单管理主页面
  功能：菜单列表展示、搜索、新增、编辑、删除、树形结构展示
-->
<template>
  <div class="menu-management">
    <!-- 搜索表单 -->
    <a-card class="search-card" :bordered="false">
      <a-form layout="inline" :model="searchForm" @finish="handleSearch">
        <a-form-item label="菜单名称">
          <a-input
            v-model:value="searchForm.title"
            placeholder="请输入菜单名称"
            allow-clear
            style="width: 200px"
          />
        </a-form-item>
        <a-form-item label="菜单类型">
          <a-select
            v-model:value="searchForm.type"
            placeholder="请选择菜单类型"
            allow-clear
            style="width: 150px"
          >
            <a-select-option
              v-for="option in MENU_TYPE_OPTIONS"
              :key="option.value"
              :value="option.value"
            >
              {{ option.label }}
            </a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="状态">
          <a-select
            v-model:value="searchForm.status"
            placeholder="请选择状态"
            allow-clear
            style="width: 120px"
          >
            <a-select-option
              v-for="option in MENU_STATUS_OPTIONS"
              :key="option.value"
              :value="option.value"
            >
              {{ option.label }}
            </a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item>
          <a-space>
            <a-button type="primary" html-type="submit" :loading="loading">
              <template #icon><SearchOutlined /></template>
              搜索
            </a-button>
            <a-button @click="handleReset">
              <template #icon><ReloadOutlined /></template>
              重置
            </a-button>
          </a-space>
        </a-form-item>
      </a-form>
    </a-card>

    <!-- 菜单列表 -->
    <a-card :bordered="false">
      <template #title>
        <a-space>
          <span>菜单列表</span>
          <a-switch
            v-model:checked="expandAll"
            checked-children="展开"
            un-checked-children="收起"
            @change="handleExpandChange"
          />
        </a-space>
      </template>
      <template #extra>
        <a-space>
          <a-button type="primary" @click="handleCreate">
            <template #icon><PlusOutlined /></template>
            新增菜单
          </a-button>
          <a-button
            type="primary"
            danger
            :disabled="!hasSelected"
            @click="handleBatchDelete"
          >
            <template #icon><DeleteOutlined /></template>
            批量删除
          </a-button>
          <a-button @click="handleRefreshCache">
            <template #icon><ReloadOutlined /></template>
            刷新缓存
          </a-button>
        </a-space>
      </template>

      <a-table
        :columns="columns"
        :data-source="menuList"
        :pagination="false"
        :loading="loading"
        :row-selection="rowSelection"
        :expanded-row-keys="expandedRowKeys"
        row-key="id"
        :default-expand-all-rows="expandAll"
        @expand="handleExpand"
      >
        <!-- 菜单名称 -->
        <template #title="{ record }">
          <a-space>
            <component
              v-if="record.icon"
              :is="record.icon"
              class="menu-icon"
            />
            <span>{{ record.title }}</span>
          </a-space>
        </template>

        <!-- 菜单类型 -->
        <template #type="{ record }">
          <a-tag :color="getMenuTypeColor(record.type)">
            {{ getMenuTypeDesc(record.type) }}
          </a-tag>
        </template>

        <!-- 路由路径 -->
        <template #path="{ record }">
          <a-tag v-if="record.path" color="blue">{{ record.path }}</a-tag>
          <span v-else class="text-gray">-</span>
        </template>

        <!-- 权限标识 -->
        <template #permission="{ record }">
          <a-tag v-if="record.permission" color="purple">{{ record.permission }}</a-tag>
          <span v-else class="text-gray">-</span>
        </template>

        <!-- 状态 -->
        <template #status="{ record }">
          <a-tag :color="getMenuStatusColor(record.status)">
            {{ getMenuStatusDesc(record.status) }}
          </a-tag>
        </template>

        <!-- 可见性 -->
        <template #visible="{ record }">
          <a-tag :color="record.visible ? 'success' : 'default'">
            {{ record.visible ? '显示' : '隐藏' }}
          </a-tag>
        </template>

        <!-- 创建时间 -->
        <template #createTime="{ record }">
          {{ formatDateTime(record.createTime) }}
        </template>

        <!-- 操作 -->
        <template #action="{ record }">
          <a-space>
            <a-button type="link" size="small" @click="handleView(record)">
              <template #icon><EyeOutlined /></template>
              查看
            </a-button>
            <a-button type="link" size="small" @click="handleEdit(record)">
              <template #icon><EditOutlined /></template>
              编辑
            </a-button>
            <a-button type="link" size="small" @click="handleCreateChild(record)">
              <template #icon><PlusOutlined /></template>
              新增下级
            </a-button>
          </a-space>
        </template>
      </a-table>
    </a-card>

    <!-- 菜单表单模态框 -->
    <a-modal
      v-model:open="modalVisible"
      :title="modalTitle"
      :width="800"
      :confirm-loading="submitLoading"
      @ok="handleSubmit"
      @cancel="handleCancel"
    >
      <a-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 18 }"
      >
        <a-form-item label="上级菜单">
          <a-tree-select
            v-model:value="formData.parentId"
            :tree-data="parentMenuOptions"
            :field-names="{ children: 'children', label: 'title', value: 'id' }"
            placeholder="请选择上级菜单"
            allow-clear
            tree-default-expand-all
          />
        </a-form-item>
        <a-form-item label="菜单类型" name="type">
          <a-radio-group v-model:value="formData.type">
            <a-radio
              v-for="option in MENU_TYPE_OPTIONS"
              :key="option.value"
              :value="option.value"
            >
              {{ option.label }}
            </a-radio>
          </a-radio-group>
        </a-form-item>
        <a-form-item label="菜单名称" name="title">
          <a-input v-model:value="formData.title" placeholder="请输入菜单名称" />
        </a-form-item>
        <a-form-item
          v-if="formData.type !== 'F'"
          label="菜单图标"
        >
          <a-input
            v-model:value="formData.icon"
            placeholder="请输入图标名称"
            readonly
            @click="handleSelectIcon"
          >
            <template #addonAfter>
              <a-button type="text" @click="handleSelectIcon">
                <template #icon><SearchOutlined /></template>
              </a-button>
            </template>
          </a-input>
        </a-form-item>
        <a-form-item
          v-if="formData.type !== 'F'"
          label="路由路径"
          name="path"
        >
          <a-input v-model:value="formData.path" placeholder="请输入路由路径" />
        </a-form-item>
        <a-form-item
          v-if="formData.type === 'C'"
          label="组件路径"
        >
          <a-input v-model:value="formData.component" placeholder="请输入组件路径" />
        </a-form-item>
        <a-form-item label="权限标识">
          <a-input v-model:value="formData.permission" placeholder="请输入权限标识" />
        </a-form-item>
        <a-form-item label="显示排序" name="sort">
          <a-input-number
            v-model:value="formData.sort"
            :min="0"
            placeholder="请输入排序值"
            style="width: 100%"
          />
        </a-form-item>
        <a-form-item label="状态">
          <a-radio-group v-model:value="formData.status">
            <a-radio
              v-for="option in MENU_STATUS_OPTIONS"
              :key="option.value"
              :value="option.value"
            >
              {{ option.label }}
            </a-radio>
          </a-radio-group>
        </a-form-item>
        <a-form-item label="显示状态">
          <a-switch
            v-model:checked="formData.visible"
            checked-children="显示"
            un-checked-children="隐藏"
          />
        </a-form-item>
        <a-form-item
          v-if="formData.type === 'C'"
          label="是否外链"
        >
          <a-switch
            v-model:checked="formData.isFrame"
            checked-children="是"
            un-checked-children="否"
          />
        </a-form-item>
        <a-form-item
          v-if="formData.type === 'C'"
          label="是否缓存"
        >
          <a-switch
            v-model:checked="formData.isCache"
            checked-children="缓存"
            un-checked-children="不缓存"
          />
        </a-form-item>
        <a-form-item label="路由参数">
          <a-input v-model:value="formData.query" placeholder="请输入路由参数" />
        </a-form-item>
        <a-form-item label="备注">
          <a-textarea
            v-model:value="formData.remark"
            placeholder="请输入备注"
            :rows="3"
          />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 图标选择器模态框 -->
    <a-modal
      v-model:open="iconSelectorVisible"
      title="选择图标"
      :width="800"
      @ok="() => { formData.icon = selectedIcon; iconSelectorVisible = false }"
      @cancel="() => { iconSelectorVisible = false }"
    >
      <div class="icon-selector">
        <div class="icon-grid">
          <div
            v-for="icon in COMMON_MENU_ICONS"
            :key="icon"
            class="icon-item"
            :class="{ active: selectedIcon === icon }"
            @click="selectedIcon = icon"
          >
            <component :is="icon" class="icon" />
            <span class="icon-name">{{ icon }}</span>
          </div>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { message, Modal } from 'ant-design-vue'
import {
  SearchOutlined,
  ReloadOutlined,
  PlusOutlined,
  DeleteOutlined,
  EditOutlined,
  EyeOutlined,
  EyeInvisibleOutlined,
  CheckOutlined,
  StopOutlined,
  CopyOutlined,
  DownOutlined
} from '@ant-design/icons-vue'
import type { TableColumnsType, FormInstance } from 'ant-design-vue'
import MenuApi from '@/api/menu'
import type {
  Menu,
  MenuQueryParams,
  MenuCreateRequest,
  MenuUpdateRequest,
  MenuType,
  MenuStatus
} from '@/types/menu'
import {
  MENU_TYPE_OPTIONS,
  MENU_STATUS_OPTIONS,
  COMMON_MENU_ICONS,
  getMenuTypeDesc,
  getMenuTypeColor,
  getMenuStatusDesc,
  getMenuStatusColor,
  convertToMenuTreeNode,
  validateMenuPath,
  validateComponentPath,
  validatePermission
} from '@/types/menu'
import { formatDateTime } from '@/utils/date'

// 响应式数据
const loading = ref(false)
const submitLoading = ref(false)
const modalVisible = ref(false)
const iconSelectorVisible = ref(false)
const expandAll = ref(false)
const selectedIcon = ref('')
const expandedRowKeys = ref<number[]>([])

// 表单引用
const formRef = ref<FormInstance>()

// 菜单列表数据
const menuList = ref<Menu[]>([])
const selectedRowKeys = ref<number[]>([])

// 搜索表单
const searchForm = reactive<MenuQueryParams>({
  title: '',
  type: undefined,
  status: undefined
})

// 表单数据
const formData = reactive<MenuCreateRequest & { id?: number }>({
  title: '',
  type: 'M' as MenuType,
  parentId: 0,
  path: '',
  component: '',
  icon: '',
  permission: '',
  sort: 0,
  status: 1 as MenuStatus,
  visible: true,
  isFrame: false,
  isCache: true,
  query: '',
  description: '',
  remark: ''
})

// 计算属性
const modalTitle = computed(() => {
  return formData.id ? '编辑菜单' : '新增菜单'
})

const hasSelected = computed(() => {
  return selectedRowKeys.value.length > 0
})

const parentMenuOptions = computed(() => {
  const options = [{ id: 0, title: '主类目', children: [] as any[] }]
  if (menuList.value.length > 0) {
    options[0].children = convertToMenuTreeNode(menuList.value)
  }
  return options
})

// 表格列配置
const columns: TableColumnsType = [
  {
    title: '菜单名称',
    dataIndex: 'title',
    key: 'title',
    width: 200,
    slots: { customRender: 'title' }
  },
  {
    title: '菜单类型',
    dataIndex: 'type',
    key: 'type',
    width: 100,
    slots: { customRender: 'type' }
  },
  {
    title: '路由路径',
    dataIndex: 'path',
    key: 'path',
    width: 150,
    slots: { customRender: 'path' }
  },
  {
    title: '权限标识',
    dataIndex: 'permission',
    key: 'permission',
    width: 150,
    slots: { customRender: 'permission' }
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    width: 80,
    slots: { customRender: 'status' }
  },
  {
    title: '可见性',
    dataIndex: 'visible',
    key: 'visible',
    width: 80,
    slots: { customRender: 'visible' }
  },
  {
    title: '排序',
    dataIndex: 'sort',
    key: 'sort',
    width: 80
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    key: 'createTime',
    width: 150,
    slots: { customRender: 'createTime' }
  },
  {
    title: '操作',
    key: 'action',
    width: 200,
    fixed: 'right',
    slots: { customRender: 'action' }
  }
]

// 行选择配置
const rowSelection = {
  selectedRowKeys: selectedRowKeys,
  onChange: (keys: number[]) => {
    selectedRowKeys.value = keys
  }
}

// 表单验证规则
const formRules = {
  type: [{ required: true, message: '请选择菜单类型' }],
  title: [{ required: true, message: '请输入菜单名称' }],
  path: [
    { required: true, message: '请输入路由路径' },
    { validator: validateMenuPath, trigger: 'blur' }
  ],
  sort: [{ required: true, message: '请输入排序值' }]
}

// 获取菜单列表
const fetchMenuList = async () => {
  try {
    loading.value = true
    const response = await MenuApi.getTree(searchForm)
    menuList.value = response.data || []
  } catch (error) {
    console.error('获取菜单列表失败:', error)
    message.error('获取菜单列表失败')
  } finally {
    loading.value = false
  }
}

// 搜索处理
const handleSearch = () => {
  fetchMenuList()
}

// 重置搜索
const handleReset = () => {
  Object.assign(searchForm, {
    title: '',
    type: undefined,
    status: undefined
  })
  fetchMenuList()
}

// 展开/收起处理
const handleExpandChange = (checked: boolean) => {
  if (checked) {
    // 展开所有节点
    const getAllKeys = (menus: Menu[]): number[] => {
      const keys: number[] = []
      menus.forEach(menu => {
        keys.push(menu.id)
        if (menu.children && menu.children.length > 0) {
          keys.push(...getAllKeys(menu.children))
        }
      })
      return keys
    }
    expandedRowKeys.value = getAllKeys(menuList.value)
  } else {
    // 收起所有节点
    expandedRowKeys.value = []
  }
}

// 表格展开处理
const handleExpand = (expanded: boolean, record: Menu) => {
  if (expanded) {
    if (!expandedRowKeys.value.includes(record.id)) {
      expandedRowKeys.value.push(record.id)
    }
  } else {
    const index = expandedRowKeys.value.indexOf(record.id)
    if (index > -1) {
      expandedRowKeys.value.splice(index, 1)
    }
  }
}

// 新增菜单
const handleCreate = () => {
  resetFormData()
  modalVisible.value = true
}

// 新增下级菜单
const handleCreateChild = (record: Menu) => {
  resetFormData()
  formData.parentId = record.id
  modalVisible.value = true
}

// 编辑菜单
const handleEdit = (record: Menu) => {
  Object.assign(formData, {
    id: record.id,
    title: record.title,
    type: record.type,
    parentId: record.parentId,
    path: record.path,
    component: record.component,
    icon: record.icon,
    permission: record.permission,
    sort: record.sort,
    status: record.status,
    visible: record.visible,
    isFrame: record.isFrame,
    isCache: record.isCache,
    query: record.query,
    description: record.description,
    remark: record.remark
  })
  modalVisible.value = true
}

// 查看菜单详情
const handleView = (record: Menu) => {
  // 这里可以实现查看详情的逻辑
  message.info('查看功能待实现')
}

// 删除菜单
const handleDelete = (record: Menu) => {
  Modal.confirm({
    title: '确认删除',
    content: `确定要删除菜单"${record.title}"吗？`,
    onOk: async () => {
      try {
        await MenuApi.delete(record.id)
        message.success('删除成功')
        fetchMenuList()
      } catch (error) {
        console.error('删除失败:', error)
        message.error('删除失败')
      }
    }
  })
}

// 批量删除
const handleBatchDelete = () => {
  if (selectedRowKeys.value.length === 0) {
    message.warning('请选择要删除的菜单')
    return
  }

  Modal.confirm({
    title: '确认批量删除',
    content: `确定要删除选中的 ${selectedRowKeys.value.length} 个菜单吗？`,
    onOk: async () => {
      try {
        await MenuApi.batchDelete(selectedRowKeys.value)
        message.success('批量删除成功')
        selectedRowKeys.value = []
        fetchMenuList()
      } catch (error) {
        console.error('批量删除失败:', error)
        message.error('批量删除失败')
      }
    }
  })
}

// 启用菜单
const handleEnable = async (record: Menu) => {
  try {
    await MenuApi.enable(record.id)
    message.success('启用成功')
    fetchMenuList()
  } catch (error) {
    console.error('启用失败:', error)
    message.error('启用失败')
  }
}

// 禁用菜单
const handleDisable = async (record: Menu) => {
  try {
    await MenuApi.disable(record.id)
    message.success('禁用成功')
    fetchMenuList()
  } catch (error) {
    console.error('禁用失败:', error)
    message.error('禁用失败')
  }
}

// 显示菜单
const handleShow = async (record: Menu) => {
  try {
    await MenuApi.show(record.id)
    message.success('显示成功')
    fetchMenuList()
  } catch (error) {
    console.error('显示失败:', error)
    message.error('显示失败')
  }
}

// 隐藏菜单
const handleHide = async (record: Menu) => {
  try {
    await MenuApi.hide(record.id)
    message.success('隐藏成功')
    fetchMenuList()
  } catch (error) {
    console.error('隐藏失败:', error)
    message.error('隐藏失败')
  }
}

// 复制菜单
const handleCopy = async (record: Menu) => {
  try {
    await MenuApi.copy(record.id)
    message.success('复制成功')
    fetchMenuList()
  } catch (error) {
    console.error('复制失败:', error)
    message.error('复制失败')
  }
}

// 刷新缓存
const handleRefreshCache = async () => {
  try {
    await MenuApi.refreshCache()
    message.success('缓存刷新成功')
  } catch (error) {
    console.error('缓存刷新失败:', error)
    message.error('缓存刷新失败')
  }
}

// 表单提交
const handleSubmit = async () => {
  try {
    await formRef.value?.validate()
    submitLoading.value = true

    if (formData.id) {
      // 编辑
      const updateData: MenuUpdateRequest = {
        title: formData.title,
        type: formData.type,
        parentId: formData.parentId,
        path: formData.path,
        component: formData.component,
        icon: formData.icon,
        permission: formData.permission,
        sort: formData.sort,
        status: formData.status,
        visible: formData.visible,
        isFrame: formData.isFrame,
        isCache: formData.isCache,
        query: formData.query,
        description: formData.description,
        remark: formData.remark
      }
      await MenuApi.update(formData.id, updateData)
      message.success('更新成功')
    } else {
      // 新增
      const createData: MenuCreateRequest = {
        title: formData.title,
        type: formData.type,
        parentId: formData.parentId,
        path: formData.path,
        component: formData.component,
        icon: formData.icon,
        permission: formData.permission,
        sort: formData.sort,
        status: formData.status,
        visible: formData.visible,
        isFrame: formData.isFrame,
        isCache: formData.isCache,
        query: formData.query,
        description: formData.description,
        remark: formData.remark
      }
      await MenuApi.create(createData)
      message.success('创建成功')
    }

    modalVisible.value = false
    fetchMenuList()
  } catch (error) {
    console.error('提交失败:', error)
    message.error('提交失败')
  } finally {
    submitLoading.value = false
  }
}

// 取消表单
const handleCancel = () => {
  modalVisible.value = false
  resetFormData()
}

// 选择图标
const handleSelectIcon = () => {
  iconSelectorVisible.value = true
}

// 重置表单数据
const resetFormData = () => {
  Object.assign(formData, {
    id: undefined,
    title: '',
    type: 'M' as MenuType,
    parentId: 0,
    path: '',
    component: '',
    icon: '',
    permission: '',
    sort: 0,
    status: 1 as MenuStatus,
    visible: true,
    isFrame: false,
    isCache: true,
    query: '',
    description: '',
    remark: ''
  })
  formRef.value?.resetFields()
}

// 组件挂载时获取数据
onMounted(() => {
  fetchMenuList()
})
</script>

<style scoped>
.menu-management {
  padding: 16px;
}

.search-card {
  margin-bottom: 16px;
}

.menu-icon {
  font-size: 16px;
  color: #1890ff;
}

.text-gray {
  color: #999;
}

:deep(.ant-table-tbody > tr > td) {
  padding: 8px 16px;
}

:deep(.ant-table-thead > tr > th) {
  background: #fafafa;
  font-weight: 600;
}

:deep(.ant-btn-link) {
  padding: 0;
  height: auto;
}

/* 图标选择器样式 */
.icon-selector {
  max-height: 400px;
  overflow-y: auto;
}

.icon-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: 12px;
  padding: 16px 0;
}

.icon-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 12px;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s;
}

.icon-item:hover {
  border-color: #1890ff;
  background-color: #f0f8ff;
}

.icon-item.active {
  border-color: #1890ff;
  background-color: #e6f7ff;
}

.icon-item .icon {
  font-size: 24px;
  color: #1890ff;
  margin-bottom: 8px;
}

.icon-item .icon-name {
  font-size: 12px;
  color: #666;
  text-align: center;
  word-break: break-all;
}
</style>

/**
 * 报表模块路由配置
 * @description 定义报表模块的所有路由
 */

import type { RouteRecordRaw } from 'vue-router'

const reportRoutes: RouteRecordRaw[] = [
  {
    path: '/report',
    name: 'Report',
    component: () => import('../layouts/ReportLayout.vue'),
    meta: {
      title: '报表中心',
      icon: 'BarChartOutlined',
      requiresAuth: true,
      permissions: ['report:view']
    },
    children: [
      // 报表管理
      {
        path: '',
        name: 'ReportManagement',
        component: () => import('../pages/ReportManagement.vue'),
        meta: {
          title: '报表管理',
          icon: 'FileTextOutlined',
          keepAlive: true
        }
      },
      
      // 报表设计器
      {
        path: 'designer/:id?',
        name: 'ReportDesigner',
        component: () => import('../components/designer/ReportDesigner.vue'),
        meta: {
          title: '报表设计器',
          icon: 'EditOutlined',
          hideInMenu: true
        }
      },
      
      // 报表预览
      {
        path: 'preview/:id',
        name: 'ReportPreview',
        component: () => import('../pages/ReportPreview.vue'),
        meta: {
          title: '报表预览',
          icon: 'EyeOutlined',
          hideInMenu: true
        }
      },
      
      // 大屏管理
      {
        path: 'dashboard',
        name: 'DashboardManagement',
        component: () => import('../pages/DashboardManagement.vue'),
        meta: {
          title: '大屏管理',
          icon: 'DashboardOutlined',
          permissions: ['dashboard:view']
        }
      },
      
      // 大屏设计器
      {
        path: 'dashboard/designer/:id?',
        name: 'DashboardDesigner',
        component: () => import('../components/dashboard/DashboardDesigner.vue'),
        meta: {
          title: '大屏设计器',
          icon: 'EditOutlined',
          hideInMenu: true,
          permissions: ['dashboard:edit']
        }
      },
      
      // 大屏预览
      {
        path: 'dashboard/preview/:id',
        name: 'DashboardPreview',
        component: () => import('../pages/DashboardPreview.vue'),
        meta: {
          title: '大屏预览',
          icon: 'EyeOutlined',
          hideInMenu: true,
          fullscreen: true
        }
      },
      
      // 数据源管理
      {
        path: 'datasource',
        name: 'DataSourceManagement',
        component: () => import('../pages/DataSourceManagement.vue'),
        meta: {
          title: '数据源管理',
          icon: 'DatabaseOutlined',
          permissions: ['datasource:view']
        }
      },
      
      // 模板管理
      {
        path: 'template',
        name: 'TemplateManagement',
        component: () => import('../pages/TemplateManagement.vue'),
        meta: {
          title: '模板管理',
          icon: 'FileAddOutlined',
          permissions: ['template:view']
        }
      },
      
      // 报表分析
      {
        path: 'analytics',
        name: 'ReportAnalytics',
        component: () => import('../pages/ReportAnalytics.vue'),
        meta: {
          title: '报表分析',
          icon: 'LineChartOutlined',
          permissions: ['report:analytics']
        }
      },
      
      // 我的收藏
      {
        path: 'favorites',
        name: 'ReportFavorites',
        component: () => import('../pages/ReportFavorites.vue'),
        meta: {
          title: '我的收藏',
          icon: 'HeartOutlined'
        }
      },
      
      // 最近访问
      {
        path: 'recent',
        name: 'ReportRecent',
        component: () => import('../pages/ReportRecent.vue'),
        meta: {
          title: '最近访问',
          icon: 'ClockCircleOutlined'
        }
      },
      
      // 共享报表
      {
        path: 'shared',
        name: 'ReportShared',
        component: () => import('../pages/ReportShared.vue'),
        meta: {
          title: '共享报表',
          icon: 'ShareAltOutlined'
        }
      },
      
      // 报表权限
      {
        path: 'permission',
        name: 'ReportPermission',
        component: () => import('../pages/ReportPermission.vue'),
        meta: {
          title: '权限管理',
          icon: 'SafetyOutlined',
          permissions: ['report:permission']
        }
      },
      
      // 系统设置
      {
        path: 'settings',
        name: 'ReportSettings',
        component: () => import('../pages/ReportSettings.vue'),
        meta: {
          title: '系统设置',
          icon: 'SettingOutlined',
          permissions: ['report:settings']
        }
      }
    ]
  },
  
  // 公共报表访问（无需登录）
  {
    path: '/public/report/:shareToken',
    name: 'PublicReport',
    component: () => import('../pages/PublicReport.vue'),
    meta: {
      title: '公共报表',
      hideInMenu: true,
      requiresAuth: false
    }
  },
  
  // 公共大屏访问（无需登录）
  {
    path: '/public/dashboard/:shareToken',
    name: 'PublicDashboard',
    component: () => import('../pages/PublicDashboard.vue'),
    meta: {
      title: '公共大屏',
      hideInMenu: true,
      requiresAuth: false,
      fullscreen: true
    }
  },
  
  // 报表打印页面
  {
    path: '/report/print/:id',
    name: 'ReportPrint',
    component: () => import('../pages/ReportPrint.vue'),
    meta: {
      title: '报表打印',
      hideInMenu: true,
      requiresAuth: true,
      layout: 'print'
    }
  },
  
  // 报表导出页面
  {
    path: '/report/export/:id',
    name: 'ReportExport',
    component: () => import('../pages/ReportExport.vue'),
    meta: {
      title: '报表导出',
      hideInMenu: true,
      requiresAuth: true
    }
  }
]

export default reportRoutes

/**
 * 报表模块菜单配置
 */
export const reportMenus = [
  {
    key: 'report',
    title: '报表中心',
    icon: 'BarChartOutlined',
    path: '/report',
    children: [
      {
        key: 'report-management',
        title: '报表管理',
        icon: 'FileTextOutlined',
        path: '/report'
      },
      {
        key: 'dashboard-management',
        title: '大屏管理',
        icon: 'DashboardOutlined',
        path: '/report/dashboard'
      },
      {
        key: 'datasource-management',
        title: '数据源管理',
        icon: 'DatabaseOutlined',
        path: '/report/datasource'
      },
      {
        key: 'template-management',
        title: '模板管理',
        icon: 'FileAddOutlined',
        path: '/report/template'
      },
      {
        key: 'report-analytics',
        title: '报表分析',
        icon: 'LineChartOutlined',
        path: '/report/analytics'
      },
      {
        type: 'divider'
      },
      {
        key: 'report-favorites',
        title: '我的收藏',
        icon: 'HeartOutlined',
        path: '/report/favorites'
      },
      {
        key: 'report-recent',
        title: '最近访问',
        icon: 'ClockCircleOutlined',
        path: '/report/recent'
      },
      {
        key: 'report-shared',
        title: '共享报表',
        icon: 'ShareAltOutlined',
        path: '/report/shared'
      },
      {
        type: 'divider'
      },
      {
        key: 'report-permission',
        title: '权限管理',
        icon: 'SafetyOutlined',
        path: '/report/permission'
      },
      {
        key: 'report-settings',
        title: '系统设置',
        icon: 'SettingOutlined',
        path: '/report/settings'
      }
    ]
  }
]

/**
 * 报表模块权限配置
 */
export const reportPermissions = [
  // 报表权限
  {
    code: 'report:view',
    name: '查看报表',
    description: '可以查看报表列表和报表内容'
  },
  {
    code: 'report:create',
    name: '创建报表',
    description: '可以创建新的报表'
  },
  {
    code: 'report:edit',
    name: '编辑报表',
    description: '可以编辑报表配置和内容'
  },
  {
    code: 'report:delete',
    name: '删除报表',
    description: '可以删除报表'
  },
  {
    code: 'report:publish',
    name: '发布报表',
    description: '可以发布报表'
  },
  {
    code: 'report:export',
    name: '导出报表',
    description: '可以导出报表数据'
  },
  {
    code: 'report:share',
    name: '分享报表',
    description: '可以分享报表给其他用户'
  },
  {
    code: 'report:analytics',
    name: '报表分析',
    description: '可以查看报表使用分析'
  },
  {
    code: 'report:permission',
    name: '权限管理',
    description: '可以管理报表权限'
  },
  {
    code: 'report:settings',
    name: '系统设置',
    description: '可以修改报表系统设置'
  },
  
  // 大屏权限
  {
    code: 'dashboard:view',
    name: '查看大屏',
    description: '可以查看大屏列表和大屏内容'
  },
  {
    code: 'dashboard:create',
    name: '创建大屏',
    description: '可以创建新的大屏'
  },
  {
    code: 'dashboard:edit',
    name: '编辑大屏',
    description: '可以编辑大屏配置和内容'
  },
  {
    code: 'dashboard:delete',
    name: '删除大屏',
    description: '可以删除大屏'
  },
  {
    code: 'dashboard:publish',
    name: '发布大屏',
    description: '可以发布大屏'
  },
  {
    code: 'dashboard:share',
    name: '分享大屏',
    description: '可以分享大屏给其他用户'
  },
  
  // 数据源权限
  {
    code: 'datasource:view',
    name: '查看数据源',
    description: '可以查看数据源列表和配置'
  },
  {
    code: 'datasource:create',
    name: '创建数据源',
    description: '可以创建新的数据源'
  },
  {
    code: 'datasource:edit',
    name: '编辑数据源',
    description: '可以编辑数据源配置'
  },
  {
    code: 'datasource:delete',
    name: '删除数据源',
    description: '可以删除数据源'
  },
  {
    code: 'datasource:test',
    name: '测试数据源',
    description: '可以测试数据源连接'
  },
  
  // 模板权限
  {
    code: 'template:view',
    name: '查看模板',
    description: '可以查看模板列表'
  },
  {
    code: 'template:create',
    name: '创建模板',
    description: '可以创建新的模板'
  },
  {
    code: 'template:edit',
    name: '编辑模板',
    description: '可以编辑模板'
  },
  {
    code: 'template:delete',
    name: '删除模板',
    description: '可以删除模板'
  }
]

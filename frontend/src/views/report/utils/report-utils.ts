/**
 * 报表工具函数
 * @description 报表模块通用工具函数
 */

import dayjs from 'dayjs'
import { message } from 'ant-design-vue'
import type { ReportConfig, ReportField, ChartConfig } from '../types/report'
import type { DataSourceConfig } from '../types/datasource'

/**
 * 格式化时间
 * @param time 时间字符串或时间戳
 * @param format 格式化模板
 * @returns 格式化后的时间字符串
 */
export const formatTime = (time: string | number | Date, format = 'YYYY-MM-DD HH:mm:ss'): string => {
  if (!time) return '-'
  return dayjs(time).format(format)
}

/**
 * 格式化文件大小
 * @param bytes 字节数
 * @returns 格式化后的文件大小
 */
export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B'
  
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

/**
 * 格式化数字
 * @param value 数值
 * @param decimals 小数位数
 * @param thousandSeparator 是否使用千分位分隔符
 * @returns 格式化后的数字字符串
 */
export const formatNumber = (
  value: number,
  decimals = 2,
  thousandSeparator = true
): string => {
  if (typeof value !== 'number' || isNaN(value)) return '0'
  
  const fixed = value.toFixed(decimals)
  
  if (thousandSeparator) {
    const parts = fixed.split('.')
    parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ',')
    return parts.join('.')
  }
  
  return fixed
}

/**
 * 格式化百分比
 * @param value 数值
 * @param decimals 小数位数
 * @returns 格式化后的百分比字符串
 */
export const formatPercentage = (value: number, decimals = 1): string => {
  if (typeof value !== 'number' || isNaN(value)) return '0%'
  return `${(value * 100).toFixed(decimals)}%`
}

/**
 * 复制文本到剪贴板
 * @param text 要复制的文本
 * @param successMessage 成功提示消息
 */
export const copyToClipboard = async (text: string, successMessage = '已复制到剪贴板'): Promise<void> => {
  try {
    if (navigator.clipboard && window.isSecureContext) {
      await navigator.clipboard.writeText(text)
    } else {
      // 降级方案
      const textArea = document.createElement('textarea')
      textArea.value = text
      textArea.style.position = 'fixed'
      textArea.style.left = '-999999px'
      textArea.style.top = '-999999px'
      document.body.appendChild(textArea)
      textArea.focus()
      textArea.select()
      document.execCommand('copy')
      textArea.remove()
    }
    message.success(successMessage)
  } catch (error) {
    console.error('复制失败:', error)
    message.error('复制失败')
  }
}

/**
 * 生成唯一ID
 * @param prefix 前缀
 * @returns 唯一ID
 */
export const generateId = (prefix = ''): string => {
  const timestamp = Date.now().toString(36)
  const random = Math.random().toString(36).substring(2, 8)
  return `${prefix}${timestamp}${random}`
}

/**
 * 深度克隆对象
 * @param obj 要克隆的对象
 * @returns 克隆后的对象
 */
export const deepClone = <T>(obj: T): T => {
  if (obj === null || typeof obj !== 'object') {
    return obj
  }
  
  if (obj instanceof Date) {
    return new Date(obj.getTime()) as T
  }
  
  if (obj instanceof Array) {
    return obj.map(item => deepClone(item)) as T
  }
  
  if (typeof obj === 'object') {
    const clonedObj = {} as T
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        clonedObj[key] = deepClone(obj[key])
      }
    }
    return clonedObj
  }
  
  return obj
}

/**
 * 防抖函数
 * @param func 要防抖的函数
 * @param wait 等待时间
 * @returns 防抖后的函数
 */
export const debounce = <T extends (...args: any[]) => any>(
  func: T,
  wait: number
): ((...args: Parameters<T>) => void) => {
  let timeout: NodeJS.Timeout | null = null
  
  return (...args: Parameters<T>) => {
    if (timeout) {
      clearTimeout(timeout)
    }
    timeout = setTimeout(() => func(...args), wait)
  }
}

/**
 * 节流函数
 * @param func 要节流的函数
 * @param wait 等待时间
 * @returns 节流后的函数
 */
export const throttle = <T extends (...args: any[]) => any>(
  func: T,
  wait: number
): ((...args: Parameters<T>) => void) => {
  let timeout: NodeJS.Timeout | null = null
  let previous = 0
  
  return (...args: Parameters<T>) => {
    const now = Date.now()
    const remaining = wait - (now - previous)
    
    if (remaining <= 0 || remaining > wait) {
      if (timeout) {
        clearTimeout(timeout)
        timeout = null
      }
      previous = now
      func(...args)
    } else if (!timeout) {
      timeout = setTimeout(() => {
        previous = Date.now()
        timeout = null
        func(...args)
      }, remaining)
    }
  }
}

/**
 * 下载文件
 * @param url 文件URL
 * @param filename 文件名
 */
export const downloadFile = (url: string, filename?: string): void => {
  const link = document.createElement('a')
  link.href = url
  if (filename) {
    link.download = filename
  }
  link.target = '_blank'
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
}

/**
 * 获取文件扩展名
 * @param filename 文件名
 * @returns 扩展名
 */
export const getFileExtension = (filename: string): string => {
  return filename.slice((filename.lastIndexOf('.') - 1 >>> 0) + 2)
}

/**
 * 验证邮箱格式
 * @param email 邮箱地址
 * @returns 是否有效
 */
export const validateEmail = (email: string): boolean => {
  const regex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return regex.test(email)
}

/**
 * 验证URL格式
 * @param url URL地址
 * @returns 是否有效
 */
export const validateUrl = (url: string): boolean => {
  try {
    new URL(url)
    return true
  } catch {
    return false
  }
}

/**
 * 获取颜色对比度
 * @param color1 颜色1
 * @param color2 颜色2
 * @returns 对比度值
 */
export const getColorContrast = (color1: string, color2: string): number => {
  const getLuminance = (color: string) => {
    const rgb = parseInt(color.slice(1), 16)
    const r = (rgb >> 16) & 0xff
    const g = (rgb >> 8) & 0xff
    const b = (rgb >> 0) & 0xff
    
    const [rs, gs, bs] = [r, g, b].map(c => {
      c = c / 255
      return c <= 0.03928 ? c / 12.92 : Math.pow((c + 0.055) / 1.055, 2.4)
    })
    
    return 0.2126 * rs + 0.7152 * gs + 0.0722 * bs
  }
  
  const lum1 = getLuminance(color1)
  const lum2 = getLuminance(color2)
  const brightest = Math.max(lum1, lum2)
  const darkest = Math.min(lum1, lum2)
  
  return (brightest + 0.05) / (darkest + 0.05)
}

/**
 * 生成随机颜色
 * @returns 随机颜色值
 */
export const generateRandomColor = (): string => {
  return '#' + Math.floor(Math.random() * 16777215).toString(16).padStart(6, '0')
}

/**
 * 获取默认图表颜色
 * @returns 颜色数组
 */
export const getDefaultChartColors = (): string[] => {
  return [
    '#1890ff',
    '#52c41a',
    '#faad14',
    '#f5222d',
    '#722ed1',
    '#fa8c16',
    '#13c2c2',
    '#eb2f96',
    '#a0d911',
    '#2f54eb'
  ]
}

/**
 * 数据类型检测
 * @param value 值
 * @returns 数据类型
 */
export const getDataType = (value: any): string => {
  if (value === null) return 'null'
  if (value === undefined) return 'undefined'
  
  const type = typeof value
  if (type !== 'object') return type
  
  if (Array.isArray(value)) return 'array'
  if (value instanceof Date) return 'date'
  if (value instanceof RegExp) return 'regexp'
  
  return 'object'
}

/**
 * 数组去重
 * @param array 数组
 * @param key 去重键
 * @returns 去重后的数组
 */
export const uniqueArray = <T>(array: T[], key?: keyof T): T[] => {
  if (!key) {
    return [...new Set(array)]
  }
  
  const seen = new Set()
  return array.filter(item => {
    const value = item[key]
    if (seen.has(value)) {
      return false
    }
    seen.add(value)
    return true
  })
}

/**
 * 数组分组
 * @param array 数组
 * @param key 分组键
 * @returns 分组后的对象
 */
export const groupBy = <T>(array: T[], key: keyof T): Record<string, T[]> => {
  return array.reduce((groups, item) => {
    const group = String(item[key])
    if (!groups[group]) {
      groups[group] = []
    }
    groups[group].push(item)
    return groups
  }, {} as Record<string, T[]>)
}

/**
 * 计算数组统计信息
 * @param array 数值数组
 * @returns 统计信息
 */
export const calculateStats = (array: number[]) => {
  if (array.length === 0) {
    return {
      count: 0,
      sum: 0,
      avg: 0,
      min: 0,
      max: 0,
      median: 0
    }
  }
  
  const sorted = [...array].sort((a, b) => a - b)
  const sum = array.reduce((acc, val) => acc + val, 0)
  const avg = sum / array.length
  const min = sorted[0]
  const max = sorted[sorted.length - 1]
  const median = sorted.length % 2 === 0
    ? (sorted[sorted.length / 2 - 1] + sorted[sorted.length / 2]) / 2
    : sorted[Math.floor(sorted.length / 2)]
  
  return {
    count: array.length,
    sum,
    avg,
    min,
    max,
    median
  }
}

/**
 * 转换数据格式为图表数据
 * @param data 原始数据
 * @param config 图表配置
 * @returns 图表数据
 */
export const transformDataForChart = (data: any[], config: ChartConfig): any[] => {
  if (!data || data.length === 0) return []
  
  const { type, xAxis, yAxis, series } = config
  
  switch (type) {
    case 'PIE':
      return data.map(item => ({
        name: item[xAxis || 'name'],
        value: item[yAxis?.[0] || 'value']
      }))
      
    case 'BAR':
    case 'LINE':
      return data.map(item => ({
        x: item[xAxis || 'x'],
        y: item[yAxis?.[0] || 'y'],
        series: series ? item[series] : undefined
      }))
      
    default:
      return data
  }
}

/**
 * 验证报表配置
 * @param config 报表配置
 * @returns 验证结果
 */
export const validateReportConfig = (config: ReportConfig): { valid: boolean; errors: string[] } => {
  const errors: string[] = []
  
  if (!config.name || config.name.trim() === '') {
    errors.push('报表名称不能为空')
  }
  
  if (!config.dataSourceId) {
    errors.push('必须选择数据源')
  }
  
  if (!config.fields || config.fields.length === 0) {
    errors.push('至少需要选择一个字段')
  }
  
  if (config.type === 'CHART' && !config.chartConfig) {
    errors.push('图表报表必须配置图表信息')
  }
  
  return {
    valid: errors.length === 0,
    errors
  }
}

/**
 * 生成SQL查询语句
 * @param config 报表配置
 * @returns SQL语句
 */
export const generateSQL = (config: ReportConfig): string => {
  const { fields, query } = config
  
  if (query.sql) {
    return query.sql
  }
  
  const selectFields = fields.map(field => {
    if (field.aggregation) {
      return `${field.aggregation}(${field.name}) AS ${field.name}`
    }
    return field.name
  }).join(', ')
  
  let sql = `SELECT ${selectFields}`
  
  if (query.tableName) {
    sql += ` FROM ${query.tableName}`
  }
  
  if (query.where) {
    sql += ` WHERE ${query.where}`
  }
  
  if (query.groupBy && query.groupBy.length > 0) {
    sql += ` GROUP BY ${query.groupBy.join(', ')}`
  }
  
  if (query.having) {
    sql += ` HAVING ${query.having}`
  }
  
  if (query.orderBy && query.orderBy.length > 0) {
    sql += ` ORDER BY ${query.orderBy.join(', ')}`
  }
  
  if (query.limit) {
    sql += ` LIMIT ${query.limit}`
  }
  
  return sql
}

/**
 * 获取数据源连接字符串
 * @param config 数据源配置
 * @returns 连接字符串
 */
export const getConnectionString = (config: DataSourceConfig): string => {
  const { type, connection } = config
  
  switch (type) {
    case 'MYSQL':
      return `mysql://${connection.username}:***@${connection.host}:${connection.port}/${connection.database}`
    case 'POSTGRESQL':
      return `postgresql://${connection.username}:***@${connection.host}:${connection.port}/${connection.database}`
    case 'MONGODB':
      return `mongodb://${connection.username}:***@${connection.host}:${connection.port}/${connection.database}`
    case 'REST_API':
      return connection.url || ''
    default:
      return `${connection.host}:${connection.port}`
  }
}

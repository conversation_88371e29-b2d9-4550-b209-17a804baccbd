/**
 * 数据源API接口
 * @description 数据源管理相关的API接口定义
 */

import axios from 'axios'
import type { AxiosResponse } from 'axios'
import type {
  DataSourceConfig,
  DataSourceQueryParams,
  ConnectionTestResult,
  DataSourceMetadata,
  DataPreviewResult,
  SQLQueryParams,
  DataSourceStatistics,
  DataSourcePerformance,
  ApiResponse,
  PageResponse
} from '../types/datasource'

/**
 * 数据源管理API类
 */
export class DataSourceApi {
  private baseUrl = '/api/report/datasources'

  /**
   * 获取数据源列表
   * @param params 查询参数
   * @returns 数据源分页列表
   */
  async getDataSources(params?: DataSourceQueryParams): Promise<ApiResponse<PageResponse<DataSourceConfig>>> {
    const response: AxiosResponse<ApiResponse<PageResponse<DataSourceConfig>>> = await axios.get(this.baseUrl, {
      params
    })
    return response.data
  }

  /**
   * 获取数据源详情
   * @param id 数据源ID
   * @returns 数据源详情
   */
  async getDataSource(id: string): Promise<ApiResponse<DataSourceConfig>> {
    const response: AxiosResponse<ApiResponse<DataSourceConfig>> = await axios.get(`${this.baseUrl}/${id}`)
    return response.data
  }

  /**
   * 创建数据源
   * @param data 数据源配置数据
   * @returns 创建的数据源
   */
  async createDataSource(data: Partial<DataSourceConfig>): Promise<ApiResponse<DataSourceConfig>> {
    const response: AxiosResponse<ApiResponse<DataSourceConfig>> = await axios.post(this.baseUrl, data)
    return response.data
  }

  /**
   * 更新数据源
   * @param id 数据源ID
   * @param data 数据源配置数据
   * @returns 更新的数据源
   */
  async updateDataSource(id: string, data: Partial<DataSourceConfig>): Promise<ApiResponse<DataSourceConfig>> {
    const response: AxiosResponse<ApiResponse<DataSourceConfig>> = await axios.put(`${this.baseUrl}/${id}`, data)
    return response.data
  }

  /**
   * 删除数据源
   * @param id 数据源ID
   * @returns 删除结果
   */
  async deleteDataSource(id: string): Promise<ApiResponse<void>> {
    const response: AxiosResponse<ApiResponse<void>> = await axios.delete(`${this.baseUrl}/${id}`)
    return response.data
  }

  /**
   * 测试数据源连接
   * @param config 数据源配置
   * @returns 测试结果
   */
  async testConnection(config: Partial<DataSourceConfig>): Promise<ApiResponse<ConnectionTestResult>> {
    const response: AxiosResponse<ApiResponse<ConnectionTestResult>> = await axios.post(
      `${this.baseUrl}/test-connection`,
      config
    )
    return response.data
  }

  /**
   * 测试现有数据源连接
   * @param id 数据源ID
   * @returns 测试结果
   */
  async testExistingConnection(id: string): Promise<ApiResponse<ConnectionTestResult>> {
    const response: AxiosResponse<ApiResponse<ConnectionTestResult>> = await axios.post(
      `${this.baseUrl}/${id}/test-connection`
    )
    return response.data
  }

  /**
   * 获取数据源元数据
   * @param id 数据源ID
   * @param refresh 是否刷新缓存
   * @returns 元数据
   */
  async getMetadata(id: string, refresh = false): Promise<ApiResponse<DataSourceMetadata>> {
    const response: AxiosResponse<ApiResponse<DataSourceMetadata>> = await axios.get(
      `${this.baseUrl}/${id}/metadata`,
      {
        params: { refresh }
      }
    )
    return response.data
  }

  /**
   * 获取表字段信息
   * @param id 数据源ID
   * @param tableName 表名
   * @returns 字段信息
   */
  async getTableColumns(id: string, tableName: string): Promise<ApiResponse<any[]>> {
    const response: AxiosResponse<ApiResponse<any[]>> = await axios.get(
      `${this.baseUrl}/${id}/tables/${tableName}/columns`
    )
    return response.data
  }

  /**
   * 预览表数据
   * @param id 数据源ID
   * @param tableName 表名
   * @param limit 限制行数
   * @returns 预览数据
   */
  async previewTableData(id: string, tableName: string, limit = 100): Promise<ApiResponse<DataPreviewResult>> {
    const response: AxiosResponse<ApiResponse<DataPreviewResult>> = await axios.get(
      `${this.baseUrl}/${id}/tables/${tableName}/preview`,
      {
        params: { limit }
      }
    )
    return response.data
  }

  /**
   * 执行SQL查询
   * @param params SQL查询参数
   * @returns 查询结果
   */
  async executeSQL(params: SQLQueryParams): Promise<ApiResponse<DataPreviewResult>> {
    const response: AxiosResponse<ApiResponse<DataPreviewResult>> = await axios.post(
      `${this.baseUrl}/execute-sql`,
      params
    )
    return response.data
  }

  /**
   * 验证SQL语句
   * @param params SQL查询参数
   * @returns 验证结果
   */
  async validateSQL(params: SQLQueryParams): Promise<ApiResponse<{ valid: boolean; errors?: string[] }>> {
    const response: AxiosResponse<ApiResponse<{ valid: boolean; errors?: string[] }>> = await axios.post(
      `${this.baseUrl}/validate-sql`,
      params
    )
    return response.data
  }

  /**
   * 获取SQL执行计划
   * @param params SQL查询参数
   * @returns 执行计划
   */
  async getExecutionPlan(params: SQLQueryParams): Promise<ApiResponse<any>> {
    const response: AxiosResponse<ApiResponse<any>> = await axios.post(`${this.baseUrl}/execution-plan`, params)
    return response.data
  }

  /**
   * 获取数据源统计信息
   * @returns 统计信息
   */
  async getStatistics(): Promise<ApiResponse<DataSourceStatistics>> {
    const response: AxiosResponse<ApiResponse<DataSourceStatistics>> = await axios.get(`${this.baseUrl}/statistics`)
    return response.data
  }

  /**
   * 获取数据源性能监控
   * @param id 数据源ID
   * @param timeRange 时间范围
   * @returns 性能数据
   */
  async getPerformanceMetrics(
    id: string,
    timeRange?: { start: string; end: string }
  ): Promise<ApiResponse<DataSourcePerformance[]>> {
    const response: AxiosResponse<ApiResponse<DataSourcePerformance[]>> = await axios.get(
      `${this.baseUrl}/${id}/performance`,
      {
        params: timeRange
      }
    )
    return response.data
  }

  /**
   * 获取支持的数据源类型
   * @returns 数据源类型列表
   */
  async getSupportedTypes(): Promise<ApiResponse<any[]>> {
    const response: AxiosResponse<ApiResponse<any[]>> = await axios.get(`${this.baseUrl}/supported-types`)
    return response.data
  }

  /**
   * 获取数据源配置模板
   * @param type 数据源类型
   * @returns 配置模板
   */
  async getConfigTemplate(type: string): Promise<ApiResponse<any>> {
    const response: AxiosResponse<ApiResponse<any>> = await axios.get(`${this.baseUrl}/config-template/${type}`)
    return response.data
  }

  /**
   * 导入数据源配置
   * @param file 配置文件
   * @returns 导入结果
   */
  async importConfig(file: File): Promise<ApiResponse<DataSourceConfig[]>> {
    const formData = new FormData()
    formData.append('file', file)
    const response: AxiosResponse<ApiResponse<DataSourceConfig[]>> = await axios.post(
      `${this.baseUrl}/import`,
      formData,
      {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      }
    )
    return response.data
  }

  /**
   * 导出数据源配置
   * @param ids 数据源ID列表
   * @returns 导出文件URL
   */
  async exportConfig(ids: string[]): Promise<ApiResponse<{ downloadUrl: string }>> {
    const response: AxiosResponse<ApiResponse<{ downloadUrl: string }>> = await axios.post(
      `${this.baseUrl}/export`,
      { ids }
    )
    return response.data
  }

  /**
   * 批量测试连接
   * @param ids 数据源ID列表
   * @returns 测试结果
   */
  async batchTestConnection(ids: string[]): Promise<ApiResponse<ConnectionTestResult[]>> {
    const response: AxiosResponse<ApiResponse<ConnectionTestResult[]>> = await axios.post(
      `${this.baseUrl}/batch-test`,
      { ids }
    )
    return response.data
  }

  /**
   * 批量更新状态
   * @param ids 数据源ID列表
   * @param status 新状态
   * @returns 更新结果
   */
  async batchUpdateStatus(ids: string[], status: string): Promise<ApiResponse<void>> {
    const response: AxiosResponse<ApiResponse<void>> = await axios.post(`${this.baseUrl}/batch-update-status`, {
      ids,
      status
    })
    return response.data
  }

  /**
   * 获取数据源使用情况
   * @param id 数据源ID
   * @returns 使用情况
   */
  async getUsage(id: string): Promise<ApiResponse<any>> {
    const response: AxiosResponse<ApiResponse<any>> = await axios.get(`${this.baseUrl}/${id}/usage`)
    return response.data
  }

  /**
   * 获取数据源连接历史
   * @param id 数据源ID
   * @param params 查询参数
   * @returns 连接历史
   */
  async getConnectionHistory(id: string, params?: any): Promise<ApiResponse<PageResponse<any>>> {
    const response: AxiosResponse<ApiResponse<PageResponse<any>>> = await axios.get(
      `${this.baseUrl}/${id}/connection-history`,
      { params }
    )
    return response.data
  }

  /**
   * 清除数据源缓存
   * @param id 数据源ID
   * @returns 清除结果
   */
  async clearCache(id: string): Promise<ApiResponse<void>> {
    const response: AxiosResponse<ApiResponse<void>> = await axios.delete(`${this.baseUrl}/${id}/cache`)
    return response.data
  }

  /**
   * 刷新数据源元数据
   * @param id 数据源ID
   * @returns 刷新结果
   */
  async refreshMetadata(id: string): Promise<ApiResponse<DataSourceMetadata>> {
    const response: AxiosResponse<ApiResponse<DataSourceMetadata>> = await axios.post(
      `${this.baseUrl}/${id}/refresh-metadata`
    )
    return response.data
  }

  /**
   * 获取数据源健康状态
   * @param id 数据源ID
   * @returns 健康状态
   */
  async getHealthStatus(id: string): Promise<ApiResponse<any>> {
    const response: AxiosResponse<ApiResponse<any>> = await axios.get(`${this.baseUrl}/${id}/health`)
    return response.data
  }

  /**
   * 设置数据源告警规则
   * @param id 数据源ID
   * @param rules 告警规则
   * @returns 设置结果
   */
  async setAlertRules(id: string, rules: any[]): Promise<ApiResponse<void>> {
    const response: AxiosResponse<ApiResponse<void>> = await axios.put(`${this.baseUrl}/${id}/alert-rules`, { rules })
    return response.data
  }

  /**
   * 获取数据源告警历史
   * @param id 数据源ID
   * @param params 查询参数
   * @returns 告警历史
   */
  async getAlertHistory(id: string, params?: any): Promise<ApiResponse<PageResponse<any>>> {
    const response: AxiosResponse<ApiResponse<PageResponse<any>>> = await axios.get(
      `${this.baseUrl}/${id}/alert-history`,
      { params }
    )
    return response.data
  }
}

// 导出API实例
export const dataSourceApi = new DataSourceApi()

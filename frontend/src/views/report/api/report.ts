/**
 * 报表API接口
 * @description 报表管理相关的API接口定义
 */

import axios from 'axios'
import type { AxiosResponse } from 'axios'
import type {
  ReportConfig,
  ReportQueryParams,
  ReportExecuteResult,
  ReportExportParams,
  ApiResponse,
  PageResponse
} from '../types/report'

/**
 * 报表管理API类
 */
export class ReportApi {
  private baseUrl = '/api/report'

  /**
   * 获取报表列表
   * @param params 查询参数
   * @returns 报表分页列表
   */
  async getReports(params?: ReportQueryParams): Promise<ApiResponse<PageResponse<ReportConfig>>> {
    const response: AxiosResponse<ApiResponse<PageResponse<ReportConfig>>> = await axios.get(
      `${this.baseUrl}/reports`,
      { params }
    )
    return response.data
  }

  /**
   * 获取报表详情
   * @param id 报表ID
   * @returns 报表详情
   */
  async getReport(id: string): Promise<ApiResponse<ReportConfig>> {
    const response: AxiosResponse<ApiResponse<ReportConfig>> = await axios.get(`${this.baseUrl}/reports/${id}`)
    return response.data
  }

  /**
   * 创建报表
   * @param data 报表配置数据
   * @returns 创建的报表
   */
  async createReport(data: Partial<ReportConfig>): Promise<ApiResponse<ReportConfig>> {
    const response: AxiosResponse<ApiResponse<ReportConfig>> = await axios.post(`${this.baseUrl}/reports`, data)
    return response.data
  }

  /**
   * 更新报表
   * @param id 报表ID
   * @param data 报表配置数据
   * @returns 更新的报表
   */
  async updateReport(id: string, data: Partial<ReportConfig>): Promise<ApiResponse<ReportConfig>> {
    const response: AxiosResponse<ApiResponse<ReportConfig>> = await axios.put(`${this.baseUrl}/reports/${id}`, data)
    return response.data
  }

  /**
   * 删除报表
   * @param id 报表ID
   * @returns 删除结果
   */
  async deleteReport(id: string): Promise<ApiResponse<void>> {
    const response: AxiosResponse<ApiResponse<void>> = await axios.delete(`${this.baseUrl}/reports/${id}`)
    return response.data
  }

  /**
   * 复制报表
   * @param id 报表ID
   * @param name 新报表名称
   * @returns 复制的报表
   */
  async copyReport(id: string, name: string): Promise<ApiResponse<ReportConfig>> {
    const response: AxiosResponse<ApiResponse<ReportConfig>> = await axios.post(
      `${this.baseUrl}/reports/${id}/copy`,
      { name }
    )
    return response.data
  }

  /**
   * 发布报表
   * @param id 报表ID
   * @returns 发布结果
   */
  async publishReport(id: string): Promise<ApiResponse<void>> {
    const response: AxiosResponse<ApiResponse<void>> = await axios.post(`${this.baseUrl}/reports/${id}/publish`)
    return response.data
  }

  /**
   * 归档报表
   * @param id 报表ID
   * @returns 归档结果
   */
  async archiveReport(id: string): Promise<ApiResponse<void>> {
    const response: AxiosResponse<ApiResponse<void>> = await axios.post(`${this.baseUrl}/reports/${id}/archive`)
    return response.data
  }

  /**
   * 执行报表
   * @param id 报表ID
   * @param parameters 参数值
   * @returns 执行结果
   */
  async executeReport(id: string, parameters?: Record<string, any>): Promise<ApiResponse<ReportExecuteResult>> {
    const response: AxiosResponse<ApiResponse<ReportExecuteResult>> = await axios.post(
      `${this.baseUrl}/reports/${id}/execute`,
      { parameters }
    )
    return response.data
  }

  /**
   * 预览报表
   * @param id 报表ID
   * @param parameters 参数值
   * @param limit 限制行数
   * @returns 预览结果
   */
  async previewReport(
    id: string,
    parameters?: Record<string, any>,
    limit = 100
  ): Promise<ApiResponse<ReportExecuteResult>> {
    const response: AxiosResponse<ApiResponse<ReportExecuteResult>> = await axios.post(
      `${this.baseUrl}/reports/${id}/preview`,
      { parameters, limit }
    )
    return response.data
  }

  /**
   * 导出报表
   * @param params 导出参数
   * @returns 导出文件URL
   */
  async exportReport(params: ReportExportParams): Promise<ApiResponse<{ downloadUrl: string }>> {
    const response: AxiosResponse<ApiResponse<{ downloadUrl: string }>> = await axios.post(
      `${this.baseUrl}/reports/export`,
      params
    )
    return response.data
  }

  /**
   * 获取报表模板列表
   * @returns 模板列表
   */
  async getReportTemplates(): Promise<ApiResponse<ReportConfig[]>> {
    const response: AxiosResponse<ApiResponse<ReportConfig[]>> = await axios.get(`${this.baseUrl}/templates`)
    return response.data
  }

  /**
   * 从模板创建报表
   * @param templateId 模板ID
   * @param name 报表名称
   * @returns 创建的报表
   */
  async createFromTemplate(templateId: string, name: string): Promise<ApiResponse<ReportConfig>> {
    const response: AxiosResponse<ApiResponse<ReportConfig>> = await axios.post(
      `${this.baseUrl}/templates/${templateId}/create`,
      { name }
    )
    return response.data
  }

  /**
   * 验证报表配置
   * @param config 报表配置
   * @returns 验证结果
   */
  async validateReport(config: Partial<ReportConfig>): Promise<ApiResponse<{ valid: boolean; errors?: string[] }>> {
    const response: AxiosResponse<ApiResponse<{ valid: boolean; errors?: string[] }>> = await axios.post(
      `${this.baseUrl}/reports/validate`,
      config
    )
    return response.data
  }

  /**
   * 获取报表权限
   * @param id 报表ID
   * @returns 权限信息
   */
  async getReportPermissions(id: string): Promise<ApiResponse<any>> {
    const response: AxiosResponse<ApiResponse<any>> = await axios.get(`${this.baseUrl}/reports/${id}/permissions`)
    return response.data
  }

  /**
   * 设置报表权限
   * @param id 报表ID
   * @param permissions 权限配置
   * @returns 设置结果
   */
  async setReportPermissions(id: string, permissions: any): Promise<ApiResponse<void>> {
    const response: AxiosResponse<ApiResponse<void>> = await axios.put(
      `${this.baseUrl}/reports/${id}/permissions`,
      permissions
    )
    return response.data
  }

  /**
   * 获取报表统计信息
   * @param dateRange 时间范围
   * @returns 统计信息
   */
  async getReportStatistics(dateRange?: { start: string; end: string }): Promise<ApiResponse<any>> {
    const response: AxiosResponse<ApiResponse<any>> = await axios.get(`${this.baseUrl}/statistics`, {
      params: dateRange
    })
    return response.data
  }

  /**
   * 获取报表使用情况
   * @param id 报表ID
   * @param dateRange 时间范围
   * @returns 使用情况
   */
  async getReportUsage(id: string, dateRange?: { start: string; end: string }): Promise<ApiResponse<any>> {
    const response: AxiosResponse<ApiResponse<any>> = await axios.get(`${this.baseUrl}/reports/${id}/usage`, {
      params: dateRange
    })
    return response.data
  }

  /**
   * 获取报表执行历史
   * @param id 报表ID
   * @param params 查询参数
   * @returns 执行历史
   */
  async getReportHistory(id: string, params?: any): Promise<ApiResponse<PageResponse<any>>> {
    const response: AxiosResponse<ApiResponse<PageResponse<any>>> = await axios.get(
      `${this.baseUrl}/reports/${id}/history`,
      { params }
    )
    return response.data
  }

  /**
   * 清除报表缓存
   * @param id 报表ID
   * @returns 清除结果
   */
  async clearReportCache(id: string): Promise<ApiResponse<void>> {
    const response: AxiosResponse<ApiResponse<void>> = await axios.delete(`${this.baseUrl}/reports/${id}/cache`)
    return response.data
  }

  /**
   * 批量操作报表
   * @param action 操作类型
   * @param reportIds 报表ID列表
   * @returns 操作结果
   */
  async batchOperation(action: string, reportIds: string[]): Promise<ApiResponse<any>> {
    const response: AxiosResponse<ApiResponse<any>> = await axios.post(`${this.baseUrl}/reports/batch`, {
      action,
      reportIds
    })
    return response.data
  }

  /**
   * 搜索报表
   * @param keyword 关键词
   * @param filters 过滤条件
   * @returns 搜索结果
   */
  async searchReports(keyword: string, filters?: any): Promise<ApiResponse<ReportConfig[]>> {
    const response: AxiosResponse<ApiResponse<ReportConfig[]>> = await axios.get(`${this.baseUrl}/reports/search`, {
      params: { keyword, ...filters }
    })
    return response.data
  }

  /**
   * 获取报表依赖关系
   * @param id 报表ID
   * @returns 依赖关系
   */
  async getReportDependencies(id: string): Promise<ApiResponse<any>> {
    const response: AxiosResponse<ApiResponse<any>> = await axios.get(`${this.baseUrl}/reports/${id}/dependencies`)
    return response.data
  }

  /**
   * 获取报表版本历史
   * @param id 报表ID
   * @returns 版本历史
   */
  async getReportVersions(id: string): Promise<ApiResponse<any[]>> {
    const response: AxiosResponse<ApiResponse<any[]>> = await axios.get(`${this.baseUrl}/reports/${id}/versions`)
    return response.data
  }

  /**
   * 恢复报表版本
   * @param id 报表ID
   * @param versionId 版本ID
   * @returns 恢复结果
   */
  async restoreReportVersion(id: string, versionId: string): Promise<ApiResponse<ReportConfig>> {
    const response: AxiosResponse<ApiResponse<ReportConfig>> = await axios.post(
      `${this.baseUrl}/reports/${id}/versions/${versionId}/restore`
    )
    return response.data
  }

  /**
   * 分享报表
   * @param id 报表ID
   * @param shareConfig 分享配置
   * @returns 分享链接
   */
  async shareReport(id: string, shareConfig: any): Promise<ApiResponse<{ shareUrl: string }>> {
    const response: AxiosResponse<ApiResponse<{ shareUrl: string }>> = await axios.post(
      `${this.baseUrl}/reports/${id}/share`,
      shareConfig
    )
    return response.data
  }

  /**
   * 取消分享报表
   * @param id 报表ID
   * @returns 取消结果
   */
  async unshareReport(id: string): Promise<ApiResponse<void>> {
    const response: AxiosResponse<ApiResponse<void>> = await axios.delete(`${this.baseUrl}/reports/${id}/share`)
    return response.data
  }

  /**
   * 订阅报表
   * @param id 报表ID
   * @param subscriptionConfig 订阅配置
   * @returns 订阅结果
   */
  async subscribeReport(id: string, subscriptionConfig: any): Promise<ApiResponse<void>> {
    const response: AxiosResponse<ApiResponse<void>> = await axios.post(
      `${this.baseUrl}/reports/${id}/subscribe`,
      subscriptionConfig
    )
    return response.data
  }

  /**
   * 取消订阅报表
   * @param id 报表ID
   * @returns 取消结果
   */
  async unsubscribeReport(id: string): Promise<ApiResponse<void>> {
    const response: AxiosResponse<ApiResponse<void>> = await axios.delete(`${this.baseUrl}/reports/${id}/subscribe`)
    return response.data
  }

  /**
   * 获取报表评论
   * @param id 报表ID
   * @returns 评论列表
   */
  async getReportComments(id: string): Promise<ApiResponse<any[]>> {
    const response: AxiosResponse<ApiResponse<any[]>> = await axios.get(`${this.baseUrl}/reports/${id}/comments`)
    return response.data
  }

  /**
   * 添加报表评论
   * @param id 报表ID
   * @param comment 评论内容
   * @returns 添加结果
   */
  async addReportComment(id: string, comment: string): Promise<ApiResponse<any>> {
    const response: AxiosResponse<ApiResponse<any>> = await axios.post(`${this.baseUrl}/reports/${id}/comments`, {
      comment
    })
    return response.data
  }

  /**
   * 收藏报表
   * @param id 报表ID
   * @returns 收藏结果
   */
  async favoriteReport(id: string): Promise<ApiResponse<void>> {
    const response: AxiosResponse<ApiResponse<void>> = await axios.post(`${this.baseUrl}/reports/${id}/favorite`)
    return response.data
  }

  /**
   * 取消收藏报表
   * @param id 报表ID
   * @returns 取消结果
   */
  async unfavoriteReport(id: string): Promise<ApiResponse<void>> {
    const response: AxiosResponse<ApiResponse<void>> = await axios.delete(`${this.baseUrl}/reports/${id}/favorite`)
    return response.data
  }

  /**
   * 获取收藏的报表
   * @returns 收藏的报表列表
   */
  async getFavoriteReports(): Promise<ApiResponse<ReportConfig[]>> {
    const response: AxiosResponse<ApiResponse<ReportConfig[]>> = await axios.get(`${this.baseUrl}/reports/favorites`)
    return response.data
  }

  /**
   * 获取最近访问的报表
   * @param limit 限制数量
   * @returns 最近访问的报表列表
   */
  async getRecentReports(limit = 10): Promise<ApiResponse<ReportConfig[]>> {
    const response: AxiosResponse<ApiResponse<ReportConfig[]>> = await axios.get(`${this.baseUrl}/reports/recent`, {
      params: { limit }
    })
    return response.data
  }
}

// 导出API实例
export const reportApi = new ReportApi()

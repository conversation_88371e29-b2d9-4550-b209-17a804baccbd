/**
 * 数据源状态管理
 * @description 使用Pinia管理数据源相关的状态
 */

import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { message } from 'ant-design-vue'
import type {
  DataSourceConfig,
  DataSourceQueryParams,
  DataSourceMetadata,
  ConnectionTestResult,
  DataSourceStatistics
} from '../types/datasource'
import { dataSourceApi } from '../api/datasource'

/**
 * 数据源状态管理
 */
export const useDataSourceStore = defineStore('datasource', () => {
  // 状态定义
  const dataSources = ref<DataSourceConfig[]>([])
  const currentDataSource = ref<DataSourceConfig | null>(null)
  const metadata = ref<Record<string, DataSourceMetadata>>({})
  const statistics = ref<DataSourceStatistics>({
    totalCount: 0,
    activeCount: 0,
    connectionSuccessRate: 0,
    averageResponseTime: 0
  })
  const loading = ref(false)
  const pagination = ref({
    current: 1,
    pageSize: 10,
    total: 0
  })

  // 计算属性
  const activeDataSources = computed(() => 
    dataSources.value.filter(ds => ds.status === 'ACTIVE')
  )
  
  const disabledDataSources = computed(() => 
    dataSources.value.filter(ds => ds.status === 'DISABLED')
  )
  
  const failedDataSources = computed(() => 
    dataSources.value.filter(ds => ds.status === 'CONNECTION_FAILED')
  )

  // 获取数据源列表
  const fetchDataSources = async (params?: DataSourceQueryParams) => {
    try {
      loading.value = true
      const response = await dataSourceApi.getDataSources({
        page: pagination.value.current,
        size: pagination.value.pageSize,
        ...params
      })
      
      if (response.code === 200) {
        dataSources.value = response.data.records
        pagination.value.total = response.data.total
        pagination.value.current = response.data.current
      }
    } catch (error) {
      console.error('获取数据源列表失败:', error)
      message.error('获取数据源列表失败')
    } finally {
      loading.value = false
    }
  }

  // 获取数据源详情
  const fetchDataSource = async (id: string) => {
    try {
      loading.value = true
      const response = await dataSourceApi.getDataSource(id)
      
      if (response.code === 200) {
        currentDataSource.value = response.data
        return response.data
      }
    } catch (error) {
      console.error('获取数据源详情失败:', error)
      message.error('获取数据源详情失败')
    } finally {
      loading.value = false
    }
  }

  // 创建数据源
  const createDataSource = async (data: Partial<DataSourceConfig>) => {
    try {
      loading.value = true
      const response = await dataSourceApi.createDataSource(data)
      
      if (response.code === 200) {
        message.success('创建数据源成功')
        await fetchDataSources()
        return response.data
      }
    } catch (error) {
      console.error('创建数据源失败:', error)
      message.error('创建数据源失败')
      throw error
    } finally {
      loading.value = false
    }
  }

  // 更新数据源
  const updateDataSource = async (id: string, data: Partial<DataSourceConfig>) => {
    try {
      loading.value = true
      const response = await dataSourceApi.updateDataSource(id, data)
      
      if (response.code === 200) {
        message.success('更新数据源成功')
        await fetchDataSources()
        if (currentDataSource.value?.id === id) {
          currentDataSource.value = response.data
        }
        return response.data
      }
    } catch (error) {
      console.error('更新数据源失败:', error)
      message.error('更新数据源失败')
      throw error
    } finally {
      loading.value = false
    }
  }

  // 删除数据源
  const deleteDataSource = async (id: string) => {
    try {
      const response = await dataSourceApi.deleteDataSource(id)
      
      if (response.code === 200) {
        message.success('删除数据源成功')
        await fetchDataSources()
      }
    } catch (error) {
      console.error('删除数据源失败:', error)
      message.error('删除数据源失败')
      throw error
    }
  }

  // 测试连接
  const testConnection = async (id: string): Promise<ConnectionTestResult> => {
    try {
      const response = await dataSourceApi.testConnection(id)
      
      if (response.code === 200) {
        return response.data
      }
      throw new Error(response.message)
    } catch (error) {
      console.error('测试连接失败:', error)
      throw error
    }
  }

  // 获取元数据
  const getMetadata = async (id: string): Promise<DataSourceMetadata> => {
    try {
      // 先检查缓存
      if (metadata.value[id]) {
        return metadata.value[id]
      }
      
      const response = await dataSourceApi.getMetadata(id)
      
      if (response.code === 200) {
        metadata.value[id] = response.data
        return response.data
      }
      throw new Error(response.message)
    } catch (error) {
      console.error('获取元数据失败:', error)
      throw error
    }
  }

  // 刷新元数据
  const refreshMetadata = async (id: string) => {
    try {
      // 清除缓存
      delete metadata.value[id]
      
      const response = await dataSourceApi.refreshMetadata(id)
      
      if (response.code === 200) {
        metadata.value[id] = response.data
        return response.data
      }
      throw new Error(response.message)
    } catch (error) {
      console.error('刷新元数据失败:', error)
      throw error
    }
  }

  // 更新数据源状态
  const updateDataSourceStatus = async (id: string, status: string) => {
    try {
      const response = await dataSourceApi.updateStatus(id, status)
      
      if (response.code === 200) {
        message.success('状态更新成功')
        await fetchDataSources()
      }
    } catch (error) {
      console.error('更新状态失败:', error)
      message.error('更新状态失败')
      throw error
    }
  }

  // 批量测试连接
  const batchTestConnection = async (ids: string[]) => {
    try {
      const response = await dataSourceApi.batchTestConnection(ids)
      
      if (response.code === 200) {
        await fetchDataSources()
        return response.data
      }
    } catch (error) {
      console.error('批量测试连接失败:', error)
      throw error
    }
  }

  // 批量更新状态
  const batchUpdateStatus = async (ids: string[], status: string) => {
    try {
      const response = await dataSourceApi.batchUpdateStatus(ids, status)
      
      if (response.code === 200) {
        message.success('批量更新状态成功')
        await fetchDataSources()
      }
    } catch (error) {
      console.error('批量更新状态失败:', error)
      throw error
    }
  }

  // 批量删除数据源
  const batchDeleteDataSources = async (ids: string[]) => {
    try {
      const response = await dataSourceApi.batchDelete(ids)
      
      if (response.code === 200) {
        message.success('批量删除成功')
        await fetchDataSources()
      }
    } catch (error) {
      console.error('批量删除失败:', error)
      throw error
    }
  }

  // 导出数据源配置
  const exportDataSources = async (ids: string[]) => {
    try {
      const response = await dataSourceApi.exportConfig(ids)
      
      if (response.code === 200) {
        // 下载文件
        const link = document.createElement('a')
        link.href = response.data.downloadUrl
        link.download = `datasources_${Date.now()}.json`
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        
        return response.data
      }
    } catch (error) {
      console.error('导出配置失败:', error)
      throw error
    }
  }

  // 导入数据源配置
  const importDataSources = async (file: File) => {
    try {
      loading.value = true
      const response = await dataSourceApi.importConfig(file)
      
      if (response.code === 200) {
        message.success('导入配置成功')
        await fetchDataSources()
        return response.data
      }
    } catch (error) {
      console.error('导入配置失败:', error)
      message.error('导入配置失败')
      throw error
    } finally {
      loading.value = false
    }
  }

  // 获取统计信息
  const fetchStatistics = async () => {
    try {
      const response = await dataSourceApi.getStatistics()
      
      if (response.code === 200) {
        statistics.value = response.data
      }
    } catch (error) {
      console.error('获取统计信息失败:', error)
    }
  }

  // 执行查询
  const executeQuery = async (id: string, query: string, parameters?: Record<string, any>) => {
    try {
      const response = await dataSourceApi.executeQuery(id, query, parameters)
      
      if (response.code === 200) {
        return response.data
      }
      throw new Error(response.message)
    } catch (error) {
      console.error('执行查询失败:', error)
      throw error
    }
  }

  // 获取字段统计
  const getFieldStats = async (id: string, tableName: string, fieldName: string) => {
    try {
      const response = await dataSourceApi.getFieldStats(id, tableName, fieldName)
      
      if (response.code === 200) {
        return response.data
      }
      throw new Error(response.message)
    } catch (error) {
      console.error('获取字段统计失败:', error)
      throw error
    }
  }

  // 获取字段预览数据
  const getFieldPreview = async (id: string, tableName: string, fieldName: string, limit = 100) => {
    try {
      const response = await dataSourceApi.getFieldPreview(id, tableName, fieldName, limit)
      
      if (response.code === 200) {
        return response.data
      }
      throw new Error(response.message)
    } catch (error) {
      console.error('获取字段预览失败:', error)
      throw error
    }
  }

  // 重置状态
  const resetState = () => {
    dataSources.value = []
    currentDataSource.value = null
    metadata.value = {}
    statistics.value = {
      totalCount: 0,
      activeCount: 0,
      connectionSuccessRate: 0,
      averageResponseTime: 0
    }
    loading.value = false
    pagination.value = {
      current: 1,
      pageSize: 10,
      total: 0
    }
  }

  // 设置分页
  const setPagination = (page: number, pageSize: number) => {
    pagination.value.current = page
    pagination.value.pageSize = pageSize
  }

  return {
    // 状态
    dataSources,
    currentDataSource,
    metadata,
    statistics,
    loading,
    pagination,
    
    // 计算属性
    activeDataSources,
    disabledDataSources,
    failedDataSources,
    
    // 方法
    fetchDataSources,
    fetchDataSource,
    createDataSource,
    updateDataSource,
    deleteDataSource,
    testConnection,
    getMetadata,
    refreshMetadata,
    updateDataSourceStatus,
    batchTestConnection,
    batchUpdateStatus,
    batchDeleteDataSources,
    exportDataSources,
    importDataSources,
    fetchStatistics,
    executeQuery,
    getFieldStats,
    getFieldPreview,
    resetState,
    setPagination
  }
})

/**
 * 报表状态管理
 * @description 使用Pinia管理报表相关的状态
 */

import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { message } from 'ant-design-vue'
import type {
  ReportConfig,
  ReportQueryParams,
  ReportExecuteResult,
  ReportExportParams,
  PageResponse
} from '../types/report'
import { reportApi } from '../api/report'

/**
 * 报表状态管理
 */
export const useReportStore = defineStore('report', () => {
  // 状态定义
  const reports = ref<ReportConfig[]>([])
  const currentReport = ref<ReportConfig | null>(null)
  const reportTemplates = ref<ReportConfig[]>([])
  const executeResult = ref<ReportExecuteResult | null>(null)
  const favoriteReports = ref<ReportConfig[]>([])
  const recentReports = ref<ReportConfig[]>([])
  const loading = ref(false)
  const executing = ref(false)
  const exporting = ref(false)
  const pagination = ref({
    current: 1,
    pageSize: 10,
    total: 0
  })

  // 计算属性
  const publishedReports = computed(() => reports.value.filter(report => report.status === 'PUBLISHED'))
  const draftReports = computed(() => reports.value.filter(report => report.status === 'DRAFT'))
  const archivedReports = computed(() => reports.value.filter(report => report.status === 'ARCHIVED'))
  const reportCount = computed(() => reports.value.length)

  // 获取报表列表
  const fetchReports = async (params?: ReportQueryParams) => {
    try {
      loading.value = true
      const response = await reportApi.getReports({
        page: pagination.value.current,
        size: pagination.value.pageSize,
        ...params
      })
      
      if (response.code === 200) {
        reports.value = response.data.records
        pagination.value.total = response.data.total
        pagination.value.current = response.data.current
      }
    } catch (error) {
      console.error('获取报表列表失败:', error)
      message.error('获取报表列表失败')
    } finally {
      loading.value = false
    }
  }

  // 获取报表详情
  const fetchReport = async (id: string) => {
    try {
      loading.value = true
      const response = await reportApi.getReport(id)
      
      if (response.code === 200) {
        currentReport.value = response.data
      }
    } catch (error) {
      console.error('获取报表详情失败:', error)
      message.error('获取报表详情失败')
    } finally {
      loading.value = false
    }
  }

  // 创建报表
  const createReport = async (data: Partial<ReportConfig>) => {
    try {
      loading.value = true
      const response = await reportApi.createReport(data)
      
      if (response.code === 200) {
        message.success('创建报表成功')
        await fetchReports()
        return response.data
      }
    } catch (error) {
      console.error('创建报表失败:', error)
      message.error('创建报表失败')
      throw error
    } finally {
      loading.value = false
    }
  }

  // 更新报表
  const updateReport = async (id: string, data: Partial<ReportConfig>) => {
    try {
      loading.value = true
      const response = await reportApi.updateReport(id, data)
      
      if (response.code === 200) {
        message.success('更新报表成功')
        await fetchReports()
        if (currentReport.value?.id === id) {
          currentReport.value = response.data
        }
        return response.data
      }
    } catch (error) {
      console.error('更新报表失败:', error)
      message.error('更新报表失败')
      throw error
    } finally {
      loading.value = false
    }
  }

  // 删除报表
  const deleteReport = async (id: string) => {
    try {
      const response = await reportApi.deleteReport(id)
      
      if (response.code === 200) {
        message.success('删除报表成功')
        await fetchReports()
      }
    } catch (error) {
      console.error('删除报表失败:', error)
      message.error('删除报表失败')
      throw error
    }
  }

  // 复制报表
  const copyReport = async (id: string, name: string) => {
    try {
      loading.value = true
      const response = await reportApi.copyReport(id, name)
      
      if (response.code === 200) {
        message.success('复制报表成功')
        await fetchReports()
        return response.data
      }
    } catch (error) {
      console.error('复制报表失败:', error)
      message.error('复制报表失败')
      throw error
    } finally {
      loading.value = false
    }
  }

  // 发布报表
  const publishReport = async (id: string) => {
    try {
      const response = await reportApi.publishReport(id)
      
      if (response.code === 200) {
        message.success('发布报表成功')
        await fetchReports()
      }
    } catch (error) {
      console.error('发布报表失败:', error)
      message.error('发布报表失败')
      throw error
    }
  }

  // 归档报表
  const archiveReport = async (id: string) => {
    try {
      const response = await reportApi.archiveReport(id)
      
      if (response.code === 200) {
        message.success('归档报表成功')
        await fetchReports()
      }
    } catch (error) {
      console.error('归档报表失败:', error)
      message.error('归档报表失败')
      throw error
    }
  }

  // 执行报表
  const executeReport = async (id: string, parameters?: Record<string, any>) => {
    try {
      executing.value = true
      const response = await reportApi.executeReport(id, parameters)
      
      if (response.code === 200) {
        executeResult.value = response.data
        message.success('报表执行成功')
        return response.data
      }
    } catch (error) {
      console.error('执行报表失败:', error)
      message.error('执行报表失败')
      throw error
    } finally {
      executing.value = false
    }
  }

  // 预览报表
  const previewReport = async (id: string, parameters?: Record<string, any>, limit = 100) => {
    try {
      executing.value = true
      const response = await reportApi.previewReport(id, parameters, limit)
      
      if (response.code === 200) {
        executeResult.value = response.data
        return response.data
      }
    } catch (error) {
      console.error('预览报表失败:', error)
      message.error('预览报表失败')
      throw error
    } finally {
      executing.value = false
    }
  }

  // 导出报表
  const exportReport = async (params: ReportExportParams) => {
    try {
      exporting.value = true
      const response = await reportApi.exportReport(params)
      
      if (response.code === 200) {
        // 下载文件
        const link = document.createElement('a')
        link.href = response.data.downloadUrl
        link.download = params.filename || `report_${Date.now()}.${params.format.toLowerCase()}`
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        
        message.success('报表导出成功')
        return response.data
      }
    } catch (error) {
      console.error('导出报表失败:', error)
      message.error('导出报表失败')
      throw error
    } finally {
      exporting.value = false
    }
  }

  // 获取报表模板
  const fetchReportTemplates = async () => {
    try {
      const response = await reportApi.getReportTemplates()
      
      if (response.code === 200) {
        reportTemplates.value = response.data
      }
    } catch (error) {
      console.error('获取报表模板失败:', error)
    }
  }

  // 从模板创建报表
  const createFromTemplate = async (templateId: string, name: string) => {
    try {
      loading.value = true
      const response = await reportApi.createFromTemplate(templateId, name)
      
      if (response.code === 200) {
        message.success('从模板创建报表成功')
        await fetchReports()
        return response.data
      }
    } catch (error) {
      console.error('从模板创建报表失败:', error)
      message.error('从模板创建报表失败')
      throw error
    } finally {
      loading.value = false
    }
  }

  // 验证报表配置
  const validateReport = async (config: Partial<ReportConfig>) => {
    try {
      const response = await reportApi.validateReport(config)
      
      if (response.code === 200) {
        return response.data
      }
    } catch (error) {
      console.error('验证报表配置失败:', error)
      throw error
    }
  }

  // 获取收藏的报表
  const fetchFavoriteReports = async () => {
    try {
      const response = await reportApi.getFavoriteReports()
      
      if (response.code === 200) {
        favoriteReports.value = response.data
      }
    } catch (error) {
      console.error('获取收藏报表失败:', error)
    }
  }

  // 收藏报表
  const favoriteReport = async (id: string) => {
    try {
      const response = await reportApi.favoriteReport(id)
      
      if (response.code === 200) {
        message.success('收藏报表成功')
        await fetchFavoriteReports()
      }
    } catch (error) {
      console.error('收藏报表失败:', error)
      message.error('收藏报表失败')
      throw error
    }
  }

  // 取消收藏报表
  const unfavoriteReport = async (id: string) => {
    try {
      const response = await reportApi.unfavoriteReport(id)
      
      if (response.code === 200) {
        message.success('取消收藏成功')
        await fetchFavoriteReports()
      }
    } catch (error) {
      console.error('取消收藏失败:', error)
      message.error('取消收藏失败')
      throw error
    }
  }

  // 获取最近访问的报表
  const fetchRecentReports = async (limit = 10) => {
    try {
      const response = await reportApi.getRecentReports(limit)
      
      if (response.code === 200) {
        recentReports.value = response.data
      }
    } catch (error) {
      console.error('获取最近访问报表失败:', error)
    }
  }

  // 搜索报表
  const searchReports = async (keyword: string, filters?: any) => {
    try {
      loading.value = true
      const response = await reportApi.searchReports(keyword, filters)
      
      if (response.code === 200) {
        reports.value = response.data
        pagination.value.total = response.data.length
      }
    } catch (error) {
      console.error('搜索报表失败:', error)
      message.error('搜索报表失败')
    } finally {
      loading.value = false
    }
  }

  // 批量操作报表
  const batchOperation = async (action: string, reportIds: string[]) => {
    try {
      loading.value = true
      const response = await reportApi.batchOperation(action, reportIds)
      
      if (response.code === 200) {
        message.success('批量操作成功')
        await fetchReports()
        return response.data
      }
    } catch (error) {
      console.error('批量操作失败:', error)
      message.error('批量操作失败')
      throw error
    } finally {
      loading.value = false
    }
  }

  // 清除报表缓存
  const clearReportCache = async (id: string) => {
    try {
      const response = await reportApi.clearReportCache(id)
      
      if (response.code === 200) {
        message.success('清除缓存成功')
      }
    } catch (error) {
      console.error('清除缓存失败:', error)
      message.error('清除缓存失败')
      throw error
    }
  }

  // 重置状态
  const resetState = () => {
    reports.value = []
    currentReport.value = null
    reportTemplates.value = []
    executeResult.value = null
    favoriteReports.value = []
    recentReports.value = []
    loading.value = false
    executing.value = false
    exporting.value = false
    pagination.value = {
      current: 1,
      pageSize: 10,
      total: 0
    }
  }

  // 设置分页
  const setPagination = (page: number, pageSize: number) => {
    pagination.value.current = page
    pagination.value.pageSize = pageSize
  }

  return {
    // 状态
    reports,
    currentReport,
    reportTemplates,
    executeResult,
    favoriteReports,
    recentReports,
    loading,
    executing,
    exporting,
    pagination,
    
    // 计算属性
    publishedReports,
    draftReports,
    archivedReports,
    reportCount,
    
    // 方法
    fetchReports,
    fetchReport,
    createReport,
    updateReport,
    deleteReport,
    copyReport,
    publishReport,
    archiveReport,
    executeReport,
    previewReport,
    exportReport,
    fetchReportTemplates,
    createFromTemplate,
    validateReport,
    fetchFavoriteReports,
    favoriteReport,
    unfavoriteReport,
    fetchRecentReports,
    searchReports,
    batchOperation,
    clearReportCache,
    resetState,
    setPagination
  }
})

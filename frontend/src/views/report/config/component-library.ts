/**
 * 组件库配置
 * @description 定义报表设计器中可用的组件库
 */

import type { ComponentLibraryConfig, ComponentTemplate } from '../types/component'

/**
 * 基础图表组件
 */
const basicCharts: ComponentTemplate[] = [
  {
    id: 'bar-chart',
    name: '柱状图',
    type: 'BAR_CHART',
    icon: 'BarChartOutlined',
    preview: '/images/charts/bar-chart.png',
    description: '用于比较不同类别的数据大小',
    usage: '适用于展示分类数据的对比，如销售额对比、产品销量对比等',
    tags: ['基础图表', '对比', '分类'],
    defaultConfig: {
      type: 'BAR_CHART',
      size: { width: 400, height: 300 },
      style: {
        background: { type: 'color', color: '#ffffff' },
        border: { width: 1, style: 'solid', color: '#f0f0f0' }
      },
      data: {
        mapping: {
          xField: '',
          yField: '',
          seriesField: ''
        }
      },
      config: {
        title: { show: true, text: '柱状图' },
        legend: { show: true, position: 'top' },
        xAxis: { show: true, name: 'X轴' },
        yAxis: { show: true, name: 'Y轴' },
        colors: ['#1890ff', '#52c41a', '#faad14', '#f5222d']
      }
    },
    configOptions: [
      { name: 'title', type: 'string', default: '柱状图', description: '图表标题' },
      { name: 'colors', type: 'array', default: '["#1890ff"]', description: '颜色配置' },
      { name: 'legend', type: 'boolean', default: 'true', description: '是否显示图例' }
    ]
  },
  {
    id: 'line-chart',
    name: '折线图',
    type: 'LINE_CHART',
    icon: 'LineChartOutlined',
    preview: '/images/charts/line-chart.png',
    description: '用于展示数据随时间或有序类别的变化趋势',
    usage: '适用于时间序列数据分析，如销售趋势、用户增长趋势等',
    tags: ['基础图表', '趋势', '时间序列'],
    defaultConfig: {
      type: 'LINE_CHART',
      size: { width: 400, height: 300 },
      style: {
        background: { type: 'color', color: '#ffffff' },
        border: { width: 1, style: 'solid', color: '#f0f0f0' }
      },
      data: {
        mapping: {
          xField: '',
          yField: '',
          seriesField: ''
        }
      },
      config: {
        title: { show: true, text: '折线图' },
        legend: { show: true, position: 'top' },
        xAxis: { show: true, name: 'X轴' },
        yAxis: { show: true, name: 'Y轴' },
        smooth: true,
        point: { show: true, size: 4 },
        colors: ['#1890ff', '#52c41a', '#faad14', '#f5222d']
      }
    },
    configOptions: [
      { name: 'smooth', type: 'boolean', default: 'true', description: '是否平滑曲线' },
      { name: 'point', type: 'boolean', default: 'true', description: '是否显示数据点' }
    ]
  },
  {
    id: 'pie-chart',
    name: '饼图',
    type: 'PIE_CHART',
    icon: 'PieChartOutlined',
    preview: '/images/charts/pie-chart.png',
    description: '用于展示各部分占整体的比例关系',
    usage: '适用于展示构成比例，如市场份额、费用构成等',
    tags: ['基础图表', '比例', '构成'],
    defaultConfig: {
      type: 'PIE_CHART',
      size: { width: 400, height: 300 },
      style: {
        background: { type: 'color', color: '#ffffff' },
        border: { width: 1, style: 'solid', color: '#f0f0f0' }
      },
      data: {
        mapping: {
          labelField: '',
          valueField: ''
        }
      },
      config: {
        title: { show: true, text: '饼图' },
        legend: { show: true, position: 'right' },
        label: { show: true, type: 'outer' },
        radius: [0, 0.8],
        colors: ['#1890ff', '#52c41a', '#faad14', '#f5222d', '#722ed1']
      }
    },
    configOptions: [
      { name: 'radius', type: 'array', default: '[0, 0.8]', description: '饼图半径' },
      { name: 'label', type: 'string', default: 'outer', description: '标签位置' }
    ]
  },
  {
    id: 'area-chart',
    name: '面积图',
    type: 'AREA_CHART',
    icon: 'AreaChartOutlined',
    preview: '/images/charts/area-chart.png',
    description: '用于展示数量随时间变化的趋势和累积效果',
    usage: '适用于展示累积数据变化，如累计销售额、用户累计增长等',
    tags: ['基础图表', '趋势', '累积'],
    defaultConfig: {
      type: 'AREA_CHART',
      size: { width: 400, height: 300 },
      style: {
        background: { type: 'color', color: '#ffffff' },
        border: { width: 1, style: 'solid', color: '#f0f0f0' }
      },
      data: {
        mapping: {
          xField: '',
          yField: '',
          seriesField: ''
        }
      },
      config: {
        title: { show: true, text: '面积图' },
        legend: { show: true, position: 'top' },
        xAxis: { show: true, name: 'X轴' },
        yAxis: { show: true, name: 'Y轴' },
        smooth: true,
        stack: false,
        colors: ['#1890ff', '#52c41a', '#faad14', '#f5222d']
      }
    },
    configOptions: [
      { name: 'stack', type: 'boolean', default: 'false', description: '是否堆叠' },
      { name: 'smooth', type: 'boolean', default: 'true', description: '是否平滑' }
    ]
  },
  {
    id: 'scatter-chart',
    name: '散点图',
    type: 'SCATTER_CHART',
    icon: 'DotChartOutlined',
    preview: '/images/charts/scatter-chart.png',
    description: '用于展示两个变量之间的相关关系',
    usage: '适用于相关性分析，如价格与销量关系、广告投入与收益关系等',
    tags: ['基础图表', '相关性', '分布'],
    defaultConfig: {
      type: 'SCATTER_CHART',
      size: { width: 400, height: 300 },
      style: {
        background: { type: 'color', color: '#ffffff' },
        border: { width: 1, style: 'solid', color: '#f0f0f0' }
      },
      data: {
        mapping: {
          xField: '',
          yField: '',
          sizeField: '',
          colorField: ''
        }
      },
      config: {
        title: { show: true, text: '散点图' },
        legend: { show: true, position: 'top' },
        xAxis: { show: true, name: 'X轴' },
        yAxis: { show: true, name: 'Y轴' },
        point: { size: 6, opacity: 0.8 },
        colors: ['#1890ff', '#52c41a', '#faad14', '#f5222d']
      }
    },
    configOptions: [
      { name: 'pointSize', type: 'number', default: '6', description: '点的大小' },
      { name: 'opacity', type: 'number', default: '0.8', description: '透明度' }
    ]
  }
]

/**
 * 高级图表组件
 */
const advancedCharts: ComponentTemplate[] = [
  {
    id: 'waterfall-chart',
    name: '瀑布图',
    type: 'WATERFALL_CHART',
    icon: 'FallOutlined',
    preview: '/images/charts/waterfall-chart.png',
    description: '用于展示数据的累积变化过程',
    usage: '适用于财务分析、利润分解等场景',
    tags: ['高级图表', '财务', '变化'],
    defaultConfig: {
      type: 'WATERFALL_CHART',
      size: { width: 400, height: 300 },
      config: {
        title: { show: true, text: '瀑布图' },
        colors: { positive: '#52c41a', negative: '#f5222d', total: '#1890ff' }
      }
    },
    configOptions: []
  },
  {
    id: 'funnel-chart',
    name: '漏斗图',
    type: 'FUNNEL_CHART',
    icon: 'FilterOutlined',
    preview: '/images/charts/funnel-chart.png',
    description: '用于展示业务流程中各阶段的转化情况',
    usage: '适用于销售漏斗、用户转化分析等',
    tags: ['高级图表', '转化', '流程'],
    defaultConfig: {
      type: 'FUNNEL_CHART',
      size: { width: 400, height: 300 },
      config: {
        title: { show: true, text: '漏斗图' },
        sort: 'descending'
      }
    },
    configOptions: []
  },
  {
    id: 'gauge-chart',
    name: '仪表盘',
    type: 'GAUGE_CHART',
    icon: 'DashboardOutlined',
    preview: '/images/charts/gauge-chart.png',
    description: '用于展示单一指标的完成情况',
    usage: '适用于KPI监控、完成率展示等',
    tags: ['高级图表', 'KPI', '监控'],
    defaultConfig: {
      type: 'GAUGE_CHART',
      size: { width: 300, height: 300 },
      config: {
        title: { show: true, text: '仪表盘' },
        min: 0,
        max: 100,
        unit: '%'
      }
    },
    configOptions: []
  }
]

/**
 * 表格组件
 */
const tableComponents: ComponentTemplate[] = [
  {
    id: 'data-table',
    name: '数据表格',
    type: 'DATA_TABLE',
    icon: 'TableOutlined',
    preview: '/images/components/data-table.png',
    description: '用于展示结构化数据',
    usage: '适用于详细数据展示、数据查询结果等',
    tags: ['表格', '数据', '详情'],
    defaultConfig: {
      type: 'DATA_TABLE',
      size: { width: 600, height: 400 },
      config: {
        pagination: true,
        pageSize: 10,
        bordered: true,
        striped: true
      }
    },
    configOptions: []
  },
  {
    id: 'pivot-table',
    name: '透视表',
    type: 'PIVOT_TABLE',
    icon: 'BorderOutlined',
    preview: '/images/components/pivot-table.png',
    description: '用于多维数据分析',
    usage: '适用于交叉分析、数据透视等',
    tags: ['表格', '透视', '分析'],
    defaultConfig: {
      type: 'PIVOT_TABLE',
      size: { width: 600, height: 400 },
      config: {
        showTotal: true,
        showSubTotal: true
      }
    },
    configOptions: []
  }
]

/**
 * 指标组件
 */
const indicatorComponents: ComponentTemplate[] = [
  {
    id: 'number-indicator',
    name: '数字指标',
    type: 'NUMBER_INDICATOR',
    icon: 'NumberOutlined',
    preview: '/images/components/number-indicator.png',
    description: '用于展示关键数字指标',
    usage: '适用于KPI展示、重要指标突出显示',
    tags: ['指标', '数字', 'KPI'],
    defaultConfig: {
      type: 'NUMBER_INDICATOR',
      size: { width: 200, height: 120 },
      config: {
        title: '指标名称',
        value: 0,
        unit: '',
        precision: 0,
        showTrend: true
      }
    },
    configOptions: []
  },
  {
    id: 'progress-bar',
    name: '进度条',
    type: 'PROGRESS_BAR',
    icon: 'LoadingOutlined',
    preview: '/images/components/progress-bar.png',
    description: '用于展示完成进度',
    usage: '适用于目标完成率、任务进度等',
    tags: ['指标', '进度', '完成率'],
    defaultConfig: {
      type: 'PROGRESS_BAR',
      size: { width: 300, height: 60 },
      config: {
        title: '进度',
        percent: 0,
        showInfo: true,
        status: 'normal'
      }
    },
    configOptions: []
  }
]

/**
 * 文本组件
 */
const textComponents: ComponentTemplate[] = [
  {
    id: 'title',
    name: '标题',
    type: 'TITLE',
    icon: 'FontSizeOutlined',
    preview: '/images/components/title.png',
    description: '用于展示标题文本',
    usage: '适用于报表标题、章节标题等',
    tags: ['文本', '标题'],
    defaultConfig: {
      type: 'TITLE',
      size: { width: 300, height: 60 },
      config: {
        text: '标题文本',
        level: 1,
        align: 'center'
      }
    },
    configOptions: []
  },
  {
    id: 'text',
    name: '文本',
    type: 'TEXT',
    icon: 'FileTextOutlined',
    preview: '/images/components/text.png',
    description: '用于展示普通文本内容',
    usage: '适用于说明文字、备注信息等',
    tags: ['文本', '内容'],
    defaultConfig: {
      type: 'TEXT',
      size: { width: 200, height: 40 },
      config: {
        text: '文本内容',
        align: 'left'
      }
    },
    configOptions: []
  }
]

/**
 * 装饰组件
 */
const decorationComponents: ComponentTemplate[] = [
  {
    id: 'divider',
    name: '分割线',
    type: 'DIVIDER',
    icon: 'MinusOutlined',
    preview: '/images/components/divider.png',
    description: '用于分割内容区域',
    usage: '适用于内容分组、视觉分割',
    tags: ['装饰', '分割'],
    defaultConfig: {
      type: 'DIVIDER',
      size: { width: 300, height: 1 },
      config: {
        orientation: 'horizontal',
        style: 'solid'
      }
    },
    configOptions: []
  },
  {
    id: 'image',
    name: '图片',
    type: 'IMAGE',
    icon: 'PictureOutlined',
    preview: '/images/components/image.png',
    description: '用于展示图片内容',
    usage: '适用于Logo、产品图片等',
    tags: ['媒体', '图片'],
    defaultConfig: {
      type: 'IMAGE',
      size: { width: 200, height: 150 },
      config: {
        src: '',
        alt: '图片',
        fit: 'cover'
      }
    },
    configOptions: []
  }
]

/**
 * 组件库配置
 */
export const componentLibrary: ComponentLibraryConfig = {
  categories: [
    {
      id: 'basic-charts',
      key: 'basic-charts',
      name: '基础图表',
      icon: 'BarChartOutlined',
      components: basicCharts
    },
    {
      id: 'advanced-charts',
      key: 'advanced-charts',
      name: '高级图表',
      icon: 'DashboardOutlined',
      components: advancedCharts
    },
    {
      id: 'tables',
      key: 'tables',
      name: '表格组件',
      icon: 'TableOutlined',
      components: tableComponents
    },
    {
      id: 'indicators',
      key: 'indicators',
      name: '指标组件',
      icon: 'NumberOutlined',
      components: indicatorComponents
    },
    {
      id: 'text',
      key: 'text',
      name: '文本组件',
      icon: 'FontSizeOutlined',
      components: textComponents
    },
    {
      id: 'decoration',
      key: 'decoration',
      name: '装饰组件',
      icon: 'BgColorsOutlined',
      components: decorationComponents
    }
  ]
}

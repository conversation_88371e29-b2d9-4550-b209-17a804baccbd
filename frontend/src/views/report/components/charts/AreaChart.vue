<!--
  面积图组件
  @description 基于ECharts的面积图组件，支持堆叠和渐变效果
-->
<template>
  <div class="area-chart" :style="containerStyle">
    <div
      ref="chartRef"
      class="chart-container"
      :style="{ width: '100%', height: '100%' }"
    ></div>
    
    <!-- 加载状态 -->
    <div v-if="loading" class="chart-loading">
      <a-spin size="large" />
    </div>
    
    <!-- 空数据状态 -->
    <div v-if="!loading && (!data || data.length === 0)" class="chart-empty">
      <a-empty description="暂无数据" />
    </div>
    
    <!-- 错误状态 -->
    <div v-if="error" class="chart-error">
      <a-result
        status="error"
        :title="error"
        sub-title="图表渲染失败"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onUnmounted, nextTick } from 'vue'
import { message } from 'ant-design-vue'
import * as echarts from 'echarts'
import type { ECharts, EChartsOption } from 'echarts'
import type { ComponentConfig } from '../../types/component'

// 组件属性
interface Props {
  /** 组件配置 */
  config: ComponentConfig
  /** 图表数据 */
  data?: any[]
  /** 是否加载中 */
  loading?: boolean
  /** 是否可交互 */
  interactive?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  data: () => [],
  loading: false,
  interactive: true
})

// 组件事件
const emit = defineEmits<{
  click: [data: any]
  legendClick: [data: any]
  dataZoom: [data: any]
  brushSelected: [data: any]
}>()

// 响应式数据
const chartRef = ref<HTMLElement>()
const chartInstance = ref<ECharts>()
const error = ref('')

// 计算属性
const containerStyle = computed(() => {
  const { size, style } = props.config
  return {
    width: `${size.width}px`,
    height: `${size.height}px`,
    background: style?.background?.color || '#ffffff',
    border: style?.border ? `${style.border.width}px ${style.border.style} ${style.border.color}` : 'none',
    borderRadius: style?.borderRadius ? `${style.borderRadius}px` : '0',
    opacity: style?.opacity || 1
  }
})

const chartOption = computed((): EChartsOption => {
  const { config: chartConfig, data: dataMapping } = props.config
  const { data } = props
  
  if (!data || data.length === 0) {
    return {}
  }
  
  try {
    // 数据处理
    const processedData = processChartData(data, dataMapping?.mapping)
    
    // 基础配置
    const option: EChartsOption = {
      title: {
        show: chartConfig.title?.show || false,
        text: chartConfig.title?.text || '',
        subtext: chartConfig.title?.subtext || '',
        left: chartConfig.title?.position || 'center',
        textStyle: {
          fontSize: chartConfig.title?.textStyle?.fontSize || 16,
          color: chartConfig.title?.textStyle?.color || '#333',
          fontWeight: chartConfig.title?.textStyle?.fontWeight || 'normal'
        }
      },
      
      legend: {
        show: chartConfig.legend?.show || false,
        type: 'scroll',
        orient: chartConfig.legend?.orient || 'horizontal',
        left: chartConfig.legend?.position === 'left' ? 'left' : 
              chartConfig.legend?.position === 'right' ? 'right' : 'center',
        top: chartConfig.legend?.position === 'top' ? 'top' : 
             chartConfig.legend?.position === 'bottom' ? 'bottom' : 'auto'
      },
      
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross',
          label: {
            backgroundColor: '#6a7985'
          }
        },
        formatter: (params: any) => {
          if (Array.isArray(params)) {
            let result = `${params[0].axisValue}<br/>`
            let total = 0
            
            params.forEach(param => {
              result += `${param.marker}${param.seriesName}: ${formatValue(param.value)}<br/>`
              total += param.value || 0
            })
            
            if (chartConfig.stack && params.length > 1) {
              result += `<hr/>总计: ${formatValue(total)}`
            }
            
            return result
          }
          return `${params.axisValue}<br/>${params.marker}${params.seriesName}: ${formatValue(params.value)}`
        }
      },
      
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true,
        show: chartConfig.grid?.show || false,
        borderColor: chartConfig.grid?.color || '#ccc'
      },
      
      xAxis: {
        type: 'category',
        boundaryGap: false,
        data: processedData.categories,
        show: chartConfig.xAxis?.show !== false,
        name: chartConfig.xAxis?.name || '',
        nameLocation: chartConfig.xAxis?.nameLocation || 'end',
        axisLine: {
          show: chartConfig.xAxis?.axisLine?.show !== false,
          lineStyle: {
            color: chartConfig.xAxis?.axisLine?.lineStyle?.color || '#333'
          }
        },
        axisTick: {
          show: chartConfig.xAxis?.axisTick?.show !== false
        },
        axisLabel: {
          show: chartConfig.xAxis?.axisLabel?.show !== false,
          rotate: chartConfig.xAxis?.axisLabel?.rotate || 0,
          fontSize: chartConfig.xAxis?.axisLabel?.textStyle?.fontSize || 12,
          color: chartConfig.xAxis?.axisLabel?.textStyle?.color || '#666'
        }
      },
      
      yAxis: {
        type: 'value',
        show: chartConfig.yAxis?.show !== false,
        name: chartConfig.yAxis?.name || '',
        nameLocation: chartConfig.yAxis?.nameLocation || 'end',
        axisLine: {
          show: chartConfig.yAxis?.axisLine?.show !== false,
          lineStyle: {
            color: chartConfig.yAxis?.axisLine?.lineStyle?.color || '#333'
          }
        },
        axisTick: {
          show: chartConfig.yAxis?.axisTick?.show !== false
        },
        axisLabel: {
          show: chartConfig.yAxis?.axisLabel?.show !== false,
          fontSize: chartConfig.yAxis?.axisLabel?.textStyle?.fontSize || 12,
          color: chartConfig.yAxis?.axisLabel?.textStyle?.color || '#666',
          formatter: (value: number) => formatValue(value)
        },
        splitLine: {
          show: true,
          lineStyle: {
            color: '#f0f0f0',
            type: 'solid'
          }
        }
      },
      
      series: processedData.series.map((seriesData, index) => ({
        name: seriesData.name,
        type: 'line',
        stack: chartConfig.stack ? 'Total' : undefined,
        data: seriesData.data,
        smooth: chartConfig.smooth !== false,
        symbol: chartConfig.point?.show !== false ? 'circle' : 'none',
        symbolSize: chartConfig.point?.size || 4,
        lineStyle: {
          width: chartConfig.lineWidth || 2,
          color: chartConfig.colors?.[index] || getDefaultColor(index)
        },
        itemStyle: {
          color: chartConfig.colors?.[index] || getDefaultColor(index)
        },
        areaStyle: {
          opacity: chartConfig.areaOpacity || 0.6,
          color: chartConfig.gradient !== false ? 
            new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { 
                offset: 0, 
                color: chartConfig.colors?.[index] || getDefaultColor(index)
              },
              { 
                offset: 1, 
                color: chartConfig.gradientEndColor || 'rgba(255, 255, 255, 0.1)'
              }
            ]) : 
            chartConfig.colors?.[index] || getDefaultColor(index)
        },
        emphasis: {
          focus: chartConfig.stack ? 'series' : 'none',
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        },
        animationDelay: (idx: number) => idx * 10,
        animationEasing: 'elasticOut'
      })),
      
      animation: chartConfig.animation?.enabled !== false,
      animationDuration: chartConfig.animation?.duration || 1000,
      animationEasing: chartConfig.animation?.easing || 'cubicOut'
    }
    
    // 工具栏配置
    if (chartConfig.toolbox?.show) {
      option.toolbox = {
        show: true,
        feature: {
          saveAsImage: { show: true, title: '保存为图片' },
          dataView: { show: true, title: '数据视图' },
          magicType: { 
            show: true, 
            type: ['line', 'bar', 'stack'],
            title: { line: '切换为折线图', bar: '切换为柱状图', stack: '切换为堆叠' }
          },
          restore: { show: true, title: '还原' },
          dataZoom: { show: true, title: { zoom: '区域缩放', back: '区域缩放还原' } }
        }
      }
    }
    
    // 数据缩放配置
    if (chartConfig.dataZoom?.show) {
      option.dataZoom = [
        {
          type: 'inside',
          start: 0,
          end: 100
        },
        {
          start: 0,
          end: 100,
          handleIcon: 'M10.7,11.9v-1.3H9.3v1.3c-4.9,0.3-8.8,4.4-8.8,9.4c0,5,3.9,9.1,8.8,9.4v1.3h1.3v-1.3c4.9-0.3,8.8-4.4,8.8-9.4C19.5,16.3,15.6,12.2,10.7,11.9z M13.3,24.4H6.7V23.1h6.6V24.4z M13.3,19.6H6.7v-1.4h6.6V19.6z',
          handleSize: '80%',
          handleStyle: {
            color: '#fff',
            shadowBlur: 3,
            shadowColor: 'rgba(0, 0, 0, 0.6)',
            shadowOffsetX: 2,
            shadowOffsetY: 2
          }
        }
      ]
    }
    
    return option
  } catch (err) {
    console.error('生成图表配置失败:', err)
    error.value = '图表配置错误'
    return {}
  }
})

// 工具方法
const processChartData = (data: any[], mapping: any) => {
  if (!mapping || !data.length) {
    return { categories: [], series: [] }
  }
  
  const { xField, yField, seriesField } = mapping
  
  if (!xField || !yField) {
    throw new Error('缺少必要的字段映射')
  }
  
  // 提取分类数据
  const categories = [...new Set(data.map(item => item[xField]))]
  
  // 处理系列数据
  let series: any[] = []
  
  if (seriesField) {
    // 多系列数据
    const seriesNames = [...new Set(data.map(item => item[seriesField]))]
    
    series = seriesNames.map(seriesName => {
      const seriesData = categories.map(category => {
        const item = data.find(d => d[xField] === category && d[seriesField] === seriesName)
        return item ? item[yField] : 0
      })
      
      return {
        name: seriesName,
        data: seriesData
      }
    })
  } else {
    // 单系列数据
    const seriesData = categories.map(category => {
      const item = data.find(d => d[xField] === category)
      return item ? item[yField] : 0
    })
    
    series = [{
      name: yField,
      data: seriesData
    }]
  }
  
  return { categories, series }
}

const formatValue = (value: any): string => {
  if (typeof value === 'number') {
    return value.toLocaleString()
  }
  return String(value)
}

const getDefaultColor = (index: number): string => {
  const colors = [
    '#1890ff', '#52c41a', '#faad14', '#f5222d', '#722ed1',
    '#fa8c16', '#13c2c2', '#eb2f96', '#a0d911', '#2f54eb'
  ]
  return colors[index % colors.length]
}

const initChart = () => {
  if (!chartRef.value) return
  
  try {
    // 销毁已存在的实例
    if (chartInstance.value) {
      chartInstance.value.dispose()
    }
    
    // 创建新实例
    chartInstance.value = echarts.init(chartRef.value)
    
    // 设置配置
    chartInstance.value.setOption(chartOption.value, true)
    
    // 绑定事件
    if (props.interactive) {
      chartInstance.value.on('click', (params) => {
        emit('click', params)
      })
      
      chartInstance.value.on('legendselectchanged', (params) => {
        emit('legendClick', params)
      })
      
      chartInstance.value.on('datazoom', (params) => {
        emit('dataZoom', params)
      })
      
      chartInstance.value.on('brushselected', (params) => {
        emit('brushSelected', params)
      })
    }
    
    error.value = ''
  } catch (err) {
    console.error('初始化图表失败:', err)
    error.value = '图表初始化失败'
    message.error('图表渲染失败')
  }
}

const resizeChart = () => {
  if (chartInstance.value) {
    chartInstance.value.resize()
  }
}

// 监听器
watch(() => props.config, () => {
  nextTick(() => {
    if (chartInstance.value) {
      chartInstance.value.setOption(chartOption.value, true)
    }
  })
}, { deep: true })

watch(() => props.data, () => {
  nextTick(() => {
    if (chartInstance.value) {
      chartInstance.value.setOption(chartOption.value, true)
    }
  })
}, { deep: true })

// 生命周期
onMounted(() => {
  nextTick(() => {
    initChart()
  })
  
  // 监听窗口大小变化
  window.addEventListener('resize', resizeChart)
})

onUnmounted(() => {
  if (chartInstance.value) {
    chartInstance.value.dispose()
  }
  window.removeEventListener('resize', resizeChart)
})

// 暴露方法
defineExpose({
  resize: resizeChart,
  getInstance: () => chartInstance.value
})
</script>

<style scoped lang="less">
.area-chart {
  position: relative;
  
  .chart-container {
    position: relative;
  }
  
  .chart-loading,
  .chart-empty,
  .chart-error {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(255, 255, 255, 0.8);
    z-index: 10;
  }
  
  .chart-error {
    flex-direction: column;
  }
}
</style>

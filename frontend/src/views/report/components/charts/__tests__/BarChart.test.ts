/**
 * <PERSON><PERSON>hart 组件单元测试
 */

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { mount } from '@vue/test-utils'
import BarChart from '../BarChart.vue'
import type { ComponentConfig } from '../../../types/component'

// Mock ECharts
const mockEChartsInstance = {
  setOption: vi.fn(),
  resize: vi.fn(),
  dispose: vi.fn(),
  on: vi.fn(),
  off: vi.fn()
}

vi.mock('echarts', () => ({
  init: vi.fn(() => mockEChartsInstance),
  graphic: {
    LinearGradient: vi.fn()
  }
}))

// Mock Ant Design Vue
vi.mock('ant-design-vue', () => ({
  message: {
    success: vi.fn(),
    error: vi.fn(),
    warning: vi.fn()
  }
}))

describe('BarChart', () => {
  let wrapper: any
  
  const mockConfig: ComponentConfig = {
    type: 'BAR_CHART' as any,
    size: { width: 400, height: 300 },
    style: {
      background: { type: 'color', color: '#ffffff' },
      border: { width: 1, style: 'solid', color: '#f0f0f0' }
    },
    data: {
      mapping: {
        xField: 'category',
        yField: 'value',
        seriesField: 'series'
      }
    },
    config: {
      title: { show: true, text: '测试柱状图' },
      legend: { show: true, position: 'top' },
      xAxis: { show: true, name: 'X轴' },
      yAxis: { show: true, name: 'Y轴' },
      colors: ['#1890ff', '#52c41a', '#faad14']
    }
  }

  const mockData = [
    { category: 'A', value: 100, series: '系列1' },
    { category: 'B', value: 200, series: '系列1' },
    { category: 'C', value: 150, series: '系列1' },
    { category: 'A', value: 120, series: '系列2' },
    { category: 'B', value: 180, series: '系列2' },
    { category: 'C', value: 160, series: '系列2' }
  ]

  beforeEach(() => {
    vi.clearAllMocks()
  })

  afterEach(() => {
    wrapper?.unmount()
  })

  it('应该正确渲染柱状图组件', () => {
    wrapper = mount(BarChart, {
      props: {
        config: mockConfig,
        data: mockData
      }
    })

    expect(wrapper.find('.bar-chart').exists()).toBe(true)
    expect(wrapper.find('.chart-container').exists()).toBe(true)
  })

  it('应该正确设置容器样式', () => {
    wrapper = mount(BarChart, {
      props: {
        config: mockConfig,
        data: mockData
      }
    })

    const container = wrapper.find('.bar-chart')
    const style = container.attributes('style')
    
    expect(style).toContain('width: 400px')
    expect(style).toContain('height: 300px')
    expect(style).toContain('background: rgb(255, 255, 255)')
  })

  it('应该正确处理图表数据', () => {
    wrapper = mount(BarChart, {
      props: {
        config: mockConfig,
        data: mockData
      }
    })

    // 验证数据处理逻辑
    const processedData = wrapper.vm.processChartData(mockData, mockConfig.data?.mapping)
    
    expect(processedData.categories).toEqual(['A', 'B', 'C'])
    expect(processedData.series).toHaveLength(2)
    expect(processedData.series[0].name).toBe('系列1')
    expect(processedData.series[1].name).toBe('系列2')
  })

  it('应该正确生成图表配置', () => {
    wrapper = mount(BarChart, {
      props: {
        config: mockConfig,
        data: mockData
      }
    })

    const chartOption = wrapper.vm.chartOption
    
    expect(chartOption.title.text).toBe('测试柱状图')
    expect(chartOption.legend.show).toBe(true)
    expect(chartOption.xAxis.type).toBe('category')
    expect(chartOption.yAxis.type).toBe('value')
    expect(chartOption.series).toHaveLength(2)
  })

  it('应该正确处理加载状态', () => {
    wrapper = mount(BarChart, {
      props: {
        config: mockConfig,
        data: mockData,
        loading: true
      }
    })

    expect(wrapper.find('.chart-loading').exists()).toBe(true)
  })

  it('应该正确处理空数据状态', () => {
    wrapper = mount(BarChart, {
      props: {
        config: mockConfig,
        data: []
      }
    })

    expect(wrapper.find('.chart-empty').exists()).toBe(true)
  })

  it('应该正确处理错误状态', async () => {
    // 模拟配置错误
    const invalidConfig = {
      ...mockConfig,
      data: {
        mapping: {
          // 缺少必要字段
        }
      }
    }

    wrapper = mount(BarChart, {
      props: {
        config: invalidConfig,
        data: mockData
      }
    })

    await wrapper.vm.$nextTick()
    
    expect(wrapper.vm.error).toBeTruthy()
  })

  it('应该正确格式化数值', () => {
    wrapper = mount(BarChart, {
      props: {
        config: mockConfig,
        data: mockData
      }
    })

    expect(wrapper.vm.formatValue(1000)).toBe('1,000')
    expect(wrapper.vm.formatValue('test')).toBe('test')
    expect(wrapper.vm.formatValue(null)).toBe('null')
  })

  it('应该正确获取默认颜色', () => {
    wrapper = mount(BarChart, {
      props: {
        config: mockConfig,
        data: mockData
      }
    })

    expect(wrapper.vm.getDefaultColor(0)).toBe('#1890ff')
    expect(wrapper.vm.getDefaultColor(1)).toBe('#52c41a')
    expect(wrapper.vm.getDefaultColor(10)).toBe('#1890ff') // 循环使用
  })

  it('应该正确绑定图表事件', () => {
    wrapper = mount(BarChart, {
      props: {
        config: mockConfig,
        data: mockData,
        interactive: true
      }
    })

    // 验证事件绑定
    expect(mockEChartsInstance.on).toHaveBeenCalledWith('click', expect.any(Function))
    expect(mockEChartsInstance.on).toHaveBeenCalledWith('legendselectchanged', expect.any(Function))
  })

  it('应该正确处理图表点击事件', () => {
    wrapper = mount(BarChart, {
      props: {
        config: mockConfig,
        data: mockData,
        interactive: true
      }
    })

    // 模拟点击事件
    const clickHandler = mockEChartsInstance.on.mock.calls.find(call => call[0] === 'click')?.[1]
    const mockParams = { dataIndex: 0, value: 100 }
    
    if (clickHandler) {
      clickHandler(mockParams)
      expect(wrapper.emitted('click')).toBeTruthy()
      expect(wrapper.emitted('click')?.[0]).toEqual([mockParams])
    }
  })

  it('应该正确处理窗口大小变化', () => {
    wrapper = mount(BarChart, {
      props: {
        config: mockConfig,
        data: mockData
      }
    })

    // 模拟窗口大小变化
    window.dispatchEvent(new Event('resize'))
    
    expect(mockEChartsInstance.resize).toHaveBeenCalled()
  })

  it('应该正确处理配置变化', async () => {
    wrapper = mount(BarChart, {
      props: {
        config: mockConfig,
        data: mockData
      }
    })

    // 更新配置
    const newConfig = {
      ...mockConfig,
      config: {
        ...mockConfig.config,
        title: { show: true, text: '更新后的标题' }
      }
    }

    await wrapper.setProps({ config: newConfig })
    await wrapper.vm.$nextTick()

    expect(mockEChartsInstance.setOption).toHaveBeenCalled()
  })

  it('应该正确处理数据变化', async () => {
    wrapper = mount(BarChart, {
      props: {
        config: mockConfig,
        data: mockData
      }
    })

    // 更新数据
    const newData = [
      { category: 'D', value: 300, series: '系列1' }
    ]

    await wrapper.setProps({ data: newData })
    await wrapper.vm.$nextTick()

    expect(mockEChartsInstance.setOption).toHaveBeenCalled()
  })

  it('应该正确处理组件销毁', () => {
    wrapper = mount(BarChart, {
      props: {
        config: mockConfig,
        data: mockData
      }
    })

    wrapper.unmount()

    expect(mockEChartsInstance.dispose).toHaveBeenCalled()
  })

  it('应该正确暴露组件方法', () => {
    wrapper = mount(BarChart, {
      props: {
        config: mockConfig,
        data: mockData
      }
    })

    // 测试暴露的方法
    expect(wrapper.vm.resize).toBeDefined()
    expect(wrapper.vm.getInstance).toBeDefined()
    
    wrapper.vm.resize()
    expect(mockEChartsInstance.resize).toHaveBeenCalled()
    
    expect(wrapper.vm.getInstance()).toBe(mockEChartsInstance)
  })

  it('应该正确处理单系列数据', () => {
    const singleSeriesData = [
      { category: 'A', value: 100 },
      { category: 'B', value: 200 },
      { category: 'C', value: 150 }
    ]

    const singleSeriesConfig = {
      ...mockConfig,
      data: {
        mapping: {
          xField: 'category',
          yField: 'value'
          // 没有 seriesField
        }
      }
    }

    wrapper = mount(BarChart, {
      props: {
        config: singleSeriesConfig,
        data: singleSeriesData
      }
    })

    const processedData = wrapper.vm.processChartData(singleSeriesData, singleSeriesConfig.data?.mapping)
    
    expect(processedData.series).toHaveLength(1)
    expect(processedData.series[0].name).toBe('value')
  })

  it('应该正确处理工具栏配置', () => {
    const configWithToolbox = {
      ...mockConfig,
      config: {
        ...mockConfig.config,
        toolbox: { show: true }
      }
    }

    wrapper = mount(BarChart, {
      props: {
        config: configWithToolbox,
        data: mockData
      }
    })

    const chartOption = wrapper.vm.chartOption
    expect(chartOption.toolbox).toBeDefined()
    expect(chartOption.toolbox.show).toBe(true)
  })
})

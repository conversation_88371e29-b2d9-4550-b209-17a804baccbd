<!--
  饼图组件
  @description 基于ECharts的饼图组件，支持环形图和玫瑰图模式
-->
<template>
  <div class="pie-chart" :style="containerStyle">
    <div
      ref="chartRef"
      class="chart-container"
      :style="{ width: '100%', height: '100%' }"
    ></div>
    
    <!-- 加载状态 -->
    <div v-if="loading" class="chart-loading">
      <a-spin size="large" />
    </div>
    
    <!-- 空数据状态 -->
    <div v-if="!loading && (!data || data.length === 0)" class="chart-empty">
      <a-empty description="暂无数据" />
    </div>
    
    <!-- 错误状态 -->
    <div v-if="error" class="chart-error">
      <a-result
        status="error"
        :title="error"
        sub-title="图表渲染失败"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onUnmounted, nextTick } from 'vue'
import { message } from 'ant-design-vue'
import * as echarts from 'echarts'
import type { ECharts, EChartsOption } from 'echarts'
import type { ComponentConfig } from '../../types/component'

// 组件属性
interface Props {
  /** 组件配置 */
  config: ComponentConfig
  /** 图表数据 */
  data?: any[]
  /** 是否加载中 */
  loading?: boolean
  /** 是否可交互 */
  interactive?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  data: () => [],
  loading: false,
  interactive: true
})

// 组件事件
const emit = defineEmits<{
  click: [data: any]
  legendClick: [data: any]
  pieSelectChanged: [data: any]
}>()

// 响应式数据
const chartRef = ref<HTMLElement>()
const chartInstance = ref<ECharts>()
const error = ref('')

// 计算属性
const containerStyle = computed(() => {
  const { size, style } = props.config
  return {
    width: `${size.width}px`,
    height: `${size.height}px`,
    background: style?.background?.color || '#ffffff',
    border: style?.border ? `${style.border.width}px ${style.border.style} ${style.border.color}` : 'none',
    borderRadius: style?.borderRadius ? `${style.borderRadius}px` : '0',
    opacity: style?.opacity || 1
  }
})

const chartOption = computed((): EChartsOption => {
  const { config: chartConfig, data: dataMapping } = props.config
  const { data } = props
  
  if (!data || data.length === 0) {
    return {}
  }
  
  try {
    // 数据处理
    const processedData = processChartData(data, dataMapping?.mapping)
    
    // 基础配置
    const option: EChartsOption = {
      title: {
        show: chartConfig.title?.show || false,
        text: chartConfig.title?.text || '',
        subtext: chartConfig.title?.subtext || '',
        left: chartConfig.title?.position || 'center',
        textStyle: {
          fontSize: chartConfig.title?.textStyle?.fontSize || 16,
          color: chartConfig.title?.textStyle?.color || '#333',
          fontWeight: chartConfig.title?.textStyle?.fontWeight || 'normal'
        }
      },
      
      legend: {
        show: chartConfig.legend?.show || false,
        type: 'scroll',
        orient: chartConfig.legend?.orient || 'vertical',
        left: chartConfig.legend?.position === 'left' ? 'left' : 
              chartConfig.legend?.position === 'right' ? 'right' : 'center',
        top: chartConfig.legend?.position === 'top' ? 'top' : 
             chartConfig.legend?.position === 'bottom' ? 'bottom' : 'middle',
        data: processedData.map(item => item.name)
      },
      
      tooltip: {
        trigger: 'item',
        formatter: (params: any) => {
          const percent = params.percent || 0
          return `${params.marker}${params.name}<br/>数值: ${formatValue(params.value)}<br/>占比: ${percent.toFixed(1)}%`
        }
      },
      
      series: [
        {
          name: chartConfig.seriesName || '数据',
          type: 'pie',
          radius: chartConfig.radius || ['0%', '75%'],
          center: chartConfig.center || ['50%', '50%'],
          roseType: chartConfig.roseType || false,
          avoidLabelOverlap: true,
          itemStyle: {
            borderRadius: chartConfig.borderRadius || 0,
            borderColor: '#fff',
            borderWidth: 2
          },
          label: {
            show: chartConfig.label?.show !== false,
            position: chartConfig.label?.position || 'outside',
            formatter: (params: any) => {
              if (chartConfig.label?.type === 'name') {
                return params.name
              } else if (chartConfig.label?.type === 'value') {
                return formatValue(params.value)
              } else if (chartConfig.label?.type === 'percent') {
                return `${params.percent.toFixed(1)}%`
              } else {
                return `${params.name}\n${formatValue(params.value)}\n${params.percent.toFixed(1)}%`
              }
            },
            fontSize: chartConfig.label?.fontSize || 12,
            color: chartConfig.label?.color || '#333'
          },
          labelLine: {
            show: chartConfig.labelLine?.show !== false,
            length: chartConfig.labelLine?.length || 15,
            length2: chartConfig.labelLine?.length2 || 15,
            smooth: chartConfig.labelLine?.smooth || false
          },
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            },
            label: {
              show: true,
              fontSize: 14,
              fontWeight: 'bold'
            }
          },
          data: processedData.map((item, index) => ({
            ...item,
            itemStyle: {
              color: chartConfig.colors?.[index] || getDefaultColor(index)
            }
          })),
          animationType: 'scale',
          animationEasing: 'elasticOut',
          animationDelay: (idx: number) => Math.random() * 200
        }
      ],
      
      animation: chartConfig.animation?.enabled !== false,
      animationDuration: chartConfig.animation?.duration || 1000,
      animationEasing: chartConfig.animation?.easing || 'cubicOut'
    }
    
    // 工具栏配置
    if (chartConfig.toolbox?.show) {
      option.toolbox = {
        show: true,
        feature: {
          saveAsImage: { show: true, title: '保存为图片' },
          dataView: { show: true, title: '数据视图' },
          restore: { show: true, title: '还原' }
        }
      }
    }
    
    return option
  } catch (err) {
    console.error('生成图表配置失败:', err)
    error.value = '图表配置错误'
    return {}
  }
})

// 工具方法
const processChartData = (data: any[], mapping: any) => {
  if (!mapping || !data.length) {
    return []
  }
  
  const { labelField, valueField } = mapping
  
  if (!labelField || !valueField) {
    throw new Error('缺少必要的字段映射')
  }
  
  // 处理饼图数据
  return data.map(item => ({
    name: item[labelField],
    value: item[valueField]
  })).filter(item => item.value != null && item.value !== '')
}

const formatValue = (value: any): string => {
  if (typeof value === 'number') {
    return value.toLocaleString()
  }
  return String(value)
}

const getDefaultColor = (index: number): string => {
  const colors = [
    '#1890ff', '#52c41a', '#faad14', '#f5222d', '#722ed1',
    '#fa8c16', '#13c2c2', '#eb2f96', '#a0d911', '#2f54eb',
    '#096dd9', '#389e0d', '#d48806', '#cf1322', '#531dab',
    '#d46b08', '#08979c', '#c41d7f', '#7cb305', '#1d39c4'
  ]
  return colors[index % colors.length]
}

const initChart = () => {
  if (!chartRef.value) return
  
  try {
    // 销毁已存在的实例
    if (chartInstance.value) {
      chartInstance.value.dispose()
    }
    
    // 创建新实例
    chartInstance.value = echarts.init(chartRef.value)
    
    // 设置配置
    chartInstance.value.setOption(chartOption.value, true)
    
    // 绑定事件
    if (props.interactive) {
      chartInstance.value.on('click', (params) => {
        emit('click', params)
      })
      
      chartInstance.value.on('legendselectchanged', (params) => {
        emit('legendClick', params)
      })
      
      chartInstance.value.on('pieselectchanged', (params) => {
        emit('pieSelectChanged', params)
      })
    }
    
    error.value = ''
  } catch (err) {
    console.error('初始化图表失败:', err)
    error.value = '图表初始化失败'
    message.error('图表渲染失败')
  }
}

const resizeChart = () => {
  if (chartInstance.value) {
    chartInstance.value.resize()
  }
}

// 监听器
watch(() => props.config, () => {
  nextTick(() => {
    if (chartInstance.value) {
      chartInstance.value.setOption(chartOption.value, true)
    }
  })
}, { deep: true })

watch(() => props.data, () => {
  nextTick(() => {
    if (chartInstance.value) {
      chartInstance.value.setOption(chartOption.value, true)
    }
  })
}, { deep: true })

// 生命周期
onMounted(() => {
  nextTick(() => {
    initChart()
  })
  
  // 监听窗口大小变化
  window.addEventListener('resize', resizeChart)
})

onUnmounted(() => {
  if (chartInstance.value) {
    chartInstance.value.dispose()
  }
  window.removeEventListener('resize', resizeChart)
})

// 暴露方法
defineExpose({
  resize: resizeChart,
  getInstance: () => chartInstance.value
})
</script>

<style scoped lang="less">
.pie-chart {
  position: relative;
  
  .chart-container {
    position: relative;
  }
  
  .chart-loading,
  .chart-empty,
  .chart-error {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(255, 255, 255, 0.8);
    z-index: 10;
  }
  
  .chart-error {
    flex-direction: column;
  }
}
</style>

<!--
  报表属性面板
  @description 报表设计器右侧属性配置面板，支持报表、字段、样式等配置
-->
<template>
  <div class="report-property-panel">
    <a-tabs v-model:activeKey="activeTab" size="small" :tabBarStyle="{ margin: 0 }">
      <!-- 报表属性 -->
      <a-tab-pane key="report" tab="报表">
        <div class="property-section">
          <h4>基本信息</h4>
          <a-form layout="vertical" size="small">
            <a-form-item label="报表名称">
              <a-input
                v-model:value="reportConfig.name"
                placeholder="请输入报表名称"
                @change="handleConfigChange"
              />
            </a-form-item>
            <a-form-item label="报表描述">
              <a-textarea
                v-model:value="reportConfig.description"
                :rows="3"
                placeholder="请输入报表描述"
                @change="handleConfigChange"
              />
            </a-form-item>
            <a-form-item label="报表类型">
              <a-select
                v-model:value="reportConfig.type"
                @change="handleTypeChange"
              >
                <a-select-option value="TABLE">表格报表</a-select-option>
                <a-select-option value="CHART">图表报表</a-select-option>
                <a-select-option value="CROSSTAB">交叉表</a-select-option>
                <a-select-option value="MIXED">混合报表</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="数据源">
              <a-select
                v-model:value="reportConfig.dataSourceId"
                placeholder="选择数据源"
                @change="handleDataSourceChange"
              >
                <a-select-option
                  v-for="ds in dataSources"
                  :key="ds.id"
                  :value="ds.id"
                >
                  {{ ds.name }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-form>
        </div>

        <div class="property-section">
          <h4>显示设置</h4>
          <a-form layout="vertical" size="small">
            <a-form-item label="页面设置">
              <a-select v-model:value="reportConfig.pageSize" @change="handleConfigChange">
                <a-select-option value="A4">A4</a-select-option>
                <a-select-option value="A3">A3</a-select-option>
                <a-select-option value="LETTER">Letter</a-select-option>
                <a-select-option value="CUSTOM">自定义</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="页面方向">
              <a-radio-group v-model:value="reportConfig.orientation" @change="handleConfigChange">
                <a-radio value="portrait">纵向</a-radio>
                <a-radio value="landscape">横向</a-radio>
              </a-radio-group>
            </a-form-item>
            <a-form-item label="显示选项">
              <a-checkbox
                v-model:checked="reportConfig.showTitle"
                @change="handleConfigChange"
              >
                显示标题
              </a-checkbox>
              <a-checkbox
                v-model:checked="reportConfig.showPageNumber"
                @change="handleConfigChange"
              >
                显示页码
              </a-checkbox>
              <a-checkbox
                v-model:checked="reportConfig.showTimestamp"
                @change="handleConfigChange"
              >
                显示时间戳
              </a-checkbox>
            </a-form-item>
          </a-form>
        </div>
      </a-tab-pane>

      <!-- 字段属性 -->
      <a-tab-pane key="field" tab="字段">
        <div v-if="selectedField" class="property-section">
          <h4>字段配置</h4>
          <a-form layout="vertical" size="small">
            <a-form-item label="字段名称">
              <a-input
                v-model:value="selectedField.name"
                @change="handleFieldChange"
              />
            </a-form-item>
            <a-form-item label="显示标签">
              <a-input
                v-model:value="selectedField.label"
                @change="handleFieldChange"
              />
            </a-form-item>
            <a-form-item label="数据类型">
              <a-select
                v-model:value="selectedField.dataType"
                @change="handleFieldChange"
              >
                <a-select-option value="STRING">文本</a-select-option>
                <a-select-option value="INTEGER">整数</a-select-option>
                <a-select-option value="DECIMAL">小数</a-select-option>
                <a-select-option value="DATE">日期</a-select-option>
                <a-select-option value="BOOLEAN">布尔</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="字段类型">
              <a-select
                v-model:value="selectedField.fieldType"
                @change="handleFieldChange"
              >
                <a-select-option value="dimension">维度</a-select-option>
                <a-select-option value="measure">度量</a-select-option>
                <a-select-option value="filter">筛选</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item v-if="selectedField.fieldType === 'measure'" label="聚合方式">
              <a-select
                v-model:value="selectedField.aggregation"
                @change="handleFieldChange"
              >
                <a-select-option value="SUM">求和</a-select-option>
                <a-select-option value="AVG">平均值</a-select-option>
                <a-select-option value="COUNT">计数</a-select-option>
                <a-select-option value="MAX">最大值</a-select-option>
                <a-select-option value="MIN">最小值</a-select-option>
                <a-select-option value="COUNT_DISTINCT">去重计数</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="功能设置">
              <a-checkbox
                v-model:checked="selectedField.visible"
                @change="handleFieldChange"
              >
                显示字段
              </a-checkbox>
              <a-checkbox
                v-model:checked="selectedField.required"
                @change="handleFieldChange"
              >
                必填字段
              </a-checkbox>
              <a-checkbox
                v-model:checked="selectedField.sort.enabled"
                @change="handleFieldChange"
              >
                启用排序
              </a-checkbox>
              <a-checkbox
                v-model:checked="selectedField.filter.enabled"
                @change="handleFieldChange"
              >
                启用筛选
              </a-checkbox>
            </a-form-item>
          </a-form>
        </div>
        <div v-else class="property-placeholder">
          <a-empty description="请选择一个字段进行配置" />
        </div>
      </a-tab-pane>

      <!-- 样式属性 -->
      <a-tab-pane key="style" tab="样式">
        <div class="property-section">
          <h4>报表样式</h4>
          <a-form layout="vertical" size="small">
            <a-form-item label="主题">
              <a-select v-model:value="styleConfig.theme" @change="handleStyleChange">
                <a-select-option value="default">默认</a-select-option>
                <a-select-option value="dark">深色</a-select-option>
                <a-select-option value="light">浅色</a-select-option>
                <a-select-option value="colorful">多彩</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="字体">
              <a-select v-model:value="styleConfig.fontFamily" @change="handleStyleChange">
                <a-select-option value="Arial">Arial</a-select-option>
                <a-select-option value="Microsoft YaHei">微软雅黑</a-select-option>
                <a-select-option value="SimSun">宋体</a-select-option>
                <a-select-option value="SimHei">黑体</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="字体大小">
              <a-input-number
                v-model:value="styleConfig.fontSize"
                :min="10"
                :max="24"
                addon-after="px"
                @change="handleStyleChange"
              />
            </a-form-item>
            <a-form-item label="主色调">
              <a-input
                v-model:value="styleConfig.primaryColor"
                type="color"
                @change="handleStyleChange"
              />
            </a-form-item>
            <a-form-item label="背景色">
              <a-input
                v-model:value="styleConfig.backgroundColor"
                type="color"
                @change="handleStyleChange"
              />
            </a-form-item>
          </a-form>
        </div>

        <div v-if="selectedField" class="property-section">
          <h4>字段样式</h4>
          <a-form layout="vertical" size="small">
            <a-form-item label="文字颜色">
              <a-input
                v-model:value="selectedField.style.color"
                type="color"
                @change="handleFieldStyleChange"
              />
            </a-form-item>
            <a-form-item label="背景色">
              <a-input
                v-model:value="selectedField.style.backgroundColor"
                type="color"
                @change="handleFieldStyleChange"
              />
            </a-form-item>
            <a-form-item label="字体大小">
              <a-input-number
                v-model:value="selectedField.style.fontSize"
                :min="10"
                :max="24"
                addon-after="px"
                @change="handleFieldStyleChange"
              />
            </a-form-item>
            <a-form-item label="字体粗细">
              <a-select
                v-model:value="selectedField.style.fontWeight"
                @change="handleFieldStyleChange"
              >
                <a-select-option value="normal">正常</a-select-option>
                <a-select-option value="bold">粗体</a-select-option>
                <a-select-option value="lighter">细体</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="对齐方式">
              <a-select
                v-model:value="selectedField.style.textAlign"
                @change="handleFieldStyleChange"
              >
                <a-select-option value="left">左对齐</a-select-option>
                <a-select-option value="center">居中</a-select-option>
                <a-select-option value="right">右对齐</a-select-option>
              </a-select>
            </a-form-item>
          </a-form>
        </div>
      </a-tab-pane>

      <!-- 条件格式 -->
      <a-tab-pane key="format" tab="条件格式">
        <div v-if="selectedField" class="property-section">
          <div class="section-header">
            <h4>条件格式规则</h4>
            <a-button size="small" type="primary" @click="addFormatRule">
              <PlusOutlined />
              添加规则
            </a-button>
          </div>
          
          <div class="format-rules">
            <div
              v-for="(rule, index) in selectedField.conditionalFormatting"
              :key="index"
              class="format-rule"
            >
              <div class="rule-header">
                <span>规则 {{ index + 1 }}</span>
                <a-button
                  type="text"
                  size="small"
                  @click="removeFormatRule(index)"
                >
                  <DeleteOutlined />
                </a-button>
              </div>
              
              <a-form layout="vertical" size="small">
                <a-form-item label="条件">
                  <a-select v-model:value="rule.condition">
                    <a-select-option value="gt">大于</a-select-option>
                    <a-select-option value="gte">大于等于</a-select-option>
                    <a-select-option value="lt">小于</a-select-option>
                    <a-select-option value="lte">小于等于</a-select-option>
                    <a-select-option value="eq">等于</a-select-option>
                    <a-select-option value="ne">不等于</a-select-option>
                    <a-select-option value="contains">包含</a-select-option>
                  </a-select>
                </a-form-item>
                <a-form-item label="值">
                  <a-input v-model:value="rule.value" />
                </a-form-item>
                <a-form-item label="样式">
                  <div class="format-style">
                    <a-input
                      v-model:value="rule.style.color"
                      type="color"
                      style="width: 60px; margin-right: 8px;"
                    />
                    <a-input
                      v-model:value="rule.style.backgroundColor"
                      type="color"
                      style="width: 60px;"
                    />
                  </div>
                </a-form-item>
              </a-form>
            </div>
          </div>
        </div>
        <div v-else class="property-placeholder">
          <a-empty description="请选择一个字段进行条件格式配置" />
        </div>
      </a-tab-pane>

      <!-- 数据设置 -->
      <a-tab-pane key="data" tab="数据">
        <div class="property-section">
          <h4>数据查询</h4>
          <a-form layout="vertical" size="small">
            <a-form-item label="查询语句">
              <a-textarea
                v-model:value="reportConfig.query.sql"
                :rows="6"
                placeholder="请输入SQL查询语句"
                @change="handleConfigChange"
              />
            </a-form-item>
            <a-form-item label="查询参数">
              <div class="query-params">
                <div
                  v-for="(param, index) in reportConfig.query.parameters"
                  :key="index"
                  class="param-item"
                >
                  <a-input
                    v-model:value="param.name"
                    placeholder="参数名"
                    style="width: 40%"
                  />
                  <a-input
                    v-model:value="param.value"
                    placeholder="参数值"
                    style="width: 40%; margin-left: 8px;"
                  />
                  <a-button
                    type="text"
                    size="small"
                    @click="removeParam(index)"
                    style="margin-left: 8px;"
                  >
                    <DeleteOutlined />
                  </a-button>
                </div>
                <a-button size="small" @click="addParam">
                  <PlusOutlined />
                  添加参数
                </a-button>
              </div>
            </a-form-item>
            <a-form-item label="缓存设置">
              <a-checkbox
                v-model:checked="reportConfig.cache.enabled"
                @change="handleConfigChange"
              >
                启用缓存
              </a-checkbox>
              <a-input-number
                v-if="reportConfig.cache.enabled"
                v-model:value="reportConfig.cache.duration"
                :min="1"
                :max="3600"
                addon-after="秒"
                style="margin-top: 8px;"
                @change="handleConfigChange"
              />
            </a-form-item>
          </a-form>
        </div>
      </a-tab-pane>
    </a-tabs>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import { PlusOutlined, DeleteOutlined } from '@ant-design/icons-vue'
import type { ReportConfig, ReportField } from '../../types/report'
import type { DataSourceConfig } from '../../types/datasource'

// 组件属性
interface Props {
  reportConfig: ReportConfig
  selectedField?: ReportField | null
  dataSources?: DataSourceConfig[]
}

const props = withDefaults(defineProps<Props>(), {
  selectedField: null,
  dataSources: () => []
})

// 组件事件
const emit = defineEmits<{
  'update:reportConfig': [config: ReportConfig]
  'update:selectedField': [field: ReportField | null]
  fieldChange: [field: ReportField]
  dataSourceChange: [dataSourceId: string]
}>()

// 响应式数据
const activeTab = ref('report')

// 样式配置
const styleConfig = reactive({
  theme: 'default',
  fontFamily: 'Microsoft YaHei',
  fontSize: 14,
  primaryColor: '#1890ff',
  backgroundColor: '#ffffff'
})

// 计算属性
const reportConfig = computed({
  get: () => props.reportConfig,
  set: (value) => emit('update:reportConfig', value)
})

const selectedField = computed({
  get: () => props.selectedField,
  set: (value) => emit('update:selectedField', value)
})

// 事件处理
const handleConfigChange = () => {
  emit('update:reportConfig', { ...reportConfig.value })
}

const handleTypeChange = (type: string) => {
  const updatedConfig = {
    ...reportConfig.value,
    type: type as any
  }
  emit('update:reportConfig', updatedConfig)
}

const handleDataSourceChange = (dataSourceId: string) => {
  const updatedConfig = {
    ...reportConfig.value,
    dataSourceId
  }
  emit('update:reportConfig', updatedConfig)
  emit('dataSourceChange', dataSourceId)
}

const handleFieldChange = () => {
  if (selectedField.value) {
    emit('fieldChange', selectedField.value)
  }
}

const handleStyleChange = () => {
  // 应用样式配置到报表
  const updatedConfig = {
    ...reportConfig.value,
    style: {
      ...reportConfig.value.style,
      ...styleConfig
    }
  }
  emit('update:reportConfig', updatedConfig)
}

const handleFieldStyleChange = () => {
  if (selectedField.value) {
    // 确保字段有样式对象
    if (!selectedField.value.style) {
      selectedField.value.style = {}
    }
    emit('fieldChange', selectedField.value)
  }
}

const addFormatRule = () => {
  if (selectedField.value) {
    if (!selectedField.value.conditionalFormatting) {
      selectedField.value.conditionalFormatting = []
    }
    
    selectedField.value.conditionalFormatting.push({
      condition: 'gt',
      value: '',
      style: {
        color: '#000000',
        backgroundColor: '#ffffff'
      }
    })
    
    emit('fieldChange', selectedField.value)
  }
}

const removeFormatRule = (index: number) => {
  if (selectedField.value?.conditionalFormatting) {
    selectedField.value.conditionalFormatting.splice(index, 1)
    emit('fieldChange', selectedField.value)
  }
}

const addParam = () => {
  if (!reportConfig.value.query) {
    reportConfig.value.query = { parameters: [] }
  }
  if (!reportConfig.value.query.parameters) {
    reportConfig.value.query.parameters = []
  }
  
  reportConfig.value.query.parameters.push({
    name: '',
    value: ''
  })
  
  handleConfigChange()
}

const removeParam = (index: number) => {
  if (reportConfig.value.query?.parameters) {
    reportConfig.value.query.parameters.splice(index, 1)
    handleConfigChange()
  }
}

// 监听选中字段变化，切换到字段标签页
watch(() => props.selectedField, (field) => {
  if (field && activeTab.value === 'report') {
    activeTab.value = 'field'
  }
})
</script>

<style scoped lang="less">
.report-property-panel {
  height: 100%;
  background: #fafafa;
  
  .property-section {
    padding: 16px;
    border-bottom: 1px solid #f0f0f0;
    
    h4 {
      margin-bottom: 12px;
      font-size: 14px;
      font-weight: 600;
      color: #262626;
    }
    
    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 12px;
      
      h4 {
        margin-bottom: 0;
      }
    }
  }
  
  .property-placeholder {
    padding: 40px 16px;
    text-align: center;
  }
  
  .format-rules {
    .format-rule {
      padding: 12px;
      margin-bottom: 8px;
      background: #fff;
      border: 1px solid #f0f0f0;
      border-radius: 6px;
      
      .rule-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;
        font-size: 12px;
        font-weight: 500;
        color: #262626;
      }
      
      .format-style {
        display: flex;
        align-items: center;
      }
    }
  }
  
  .query-params {
    .param-item {
      display: flex;
      align-items: center;
      margin-bottom: 8px;
    }
  }
}

// 标签页样式覆盖
:deep(.ant-tabs) {
  .ant-tabs-tab {
    font-size: 12px;
    padding: 8px 12px;
  }
  
  .ant-tabs-content-holder {
    overflow-y: auto;
  }
}
</style>

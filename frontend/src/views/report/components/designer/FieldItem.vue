<!--
  字段项组件
  @description 单个字段的展示和操作组件
-->
<template>
  <div class="field-item" :class="{ 'field-selected': selected }">
    <div class="field-content" @click="handleFieldClick">
      <!-- 字段图标 -->
      <div class="field-icon">
        <component :is="getFieldIcon(field.dataType)" />
      </div>
      
      <!-- 字段信息 -->
      <div class="field-info">
        <div class="field-name">{{ field.label || field.name }}</div>
        <div class="field-meta">
          <a-tag :color="getFieldTypeColor(field.fieldType)" size="small">
            {{ getFieldTypeText(field.fieldType) }}
          </a-tag>
          <a-tag v-if="field.aggregation" color="blue" size="small">
            {{ getAggregationText(field.aggregation) }}
          </a-tag>
          <span class="field-data-type">{{ getDataTypeText(field.dataType) }}</span>
        </div>
      </div>
      
      <!-- 字段状态指示器 -->
      <div class="field-indicators">
        <a-tooltip v-if="field.sort?.enabled" title="已启用排序">
          <SortAscendingOutlined class="indicator-icon sort" />
        </a-tooltip>
        <a-tooltip v-if="field.filter?.enabled" title="已启用筛选">
          <FilterOutlined class="indicator-icon filter" />
        </a-tooltip>
        <a-tooltip v-if="field.conditionalFormatting?.length" title="已设置条件格式">
          <BgColorsOutlined class="indicator-icon format" />
        </a-tooltip>
        <a-tooltip v-if="!field.visible" title="字段已隐藏">
          <EyeInvisibleOutlined class="indicator-icon hidden" />
        </a-tooltip>
      </div>
    </div>
    
    <!-- 字段操作菜单 -->
    <div class="field-actions">
      <a-dropdown :trigger="['click']" placement="bottomRight">
        <a-button type="text" size="small" class="action-button">
          <MoreOutlined />
        </a-button>
        <template #overlay>
          <a-menu @click="handleMenuClick">
            <a-menu-item key="configure">
              <SettingOutlined />
              配置字段
            </a-menu-item>
            <a-menu-item key="rename">
              <EditOutlined />
              重命名
            </a-menu-item>
            <a-menu-item key="duplicate">
              <CopyOutlined />
              复制字段
            </a-menu-item>
            <a-menu-divider />
            <a-menu-item key="sort" :disabled="field.fieldType === 'filter'">
              <SortAscendingOutlined />
              {{ field.sort?.enabled ? '禁用排序' : '启用排序' }}
            </a-menu-item>
            <a-menu-item key="filter" :disabled="field.fieldType === 'measure'">
              <FilterOutlined />
              {{ field.filter?.enabled ? '禁用筛选' : '启用筛选' }}
            </a-menu-item>
            <a-menu-item key="visibility">
              <component :is="field.visible ? 'EyeInvisibleOutlined' : 'EyeOutlined'" />
              {{ field.visible ? '隐藏字段' : '显示字段' }}
            </a-menu-item>
            <a-menu-divider />
            <a-menu-item key="remove" class="danger-item">
              <DeleteOutlined />
              移除字段
            </a-menu-item>
          </a-menu>
        </template>
      </a-dropdown>
    </div>
    
    <!-- 拖拽手柄 -->
    <div class="drag-handle">
      <HolderOutlined />
    </div>

    <!-- 字段重命名弹窗 -->
    <a-modal
      v-model:visible="renameVisible"
      title="重命名字段"
      width="400px"
      @ok="handleRename"
    >
      <a-form layout="vertical">
        <a-form-item label="字段名称">
          <a-input
            v-model:value="newFieldName"
            placeholder="请输入字段名称"
            @press-enter="handleRename"
          />
        </a-form-item>
        <a-form-item label="显示标签">
          <a-input
            v-model:value="newFieldLabel"
            placeholder="请输入显示标签"
            @press-enter="handleRename"
          />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { message } from 'ant-design-vue'
import {
  MoreOutlined,
  SettingOutlined,
  EditOutlined,
  CopyOutlined,
  SortAscendingOutlined,
  FilterOutlined,
  BgColorsOutlined,
  EyeOutlined,
  EyeInvisibleOutlined,
  DeleteOutlined,
  HolderOutlined,
  FieldStringOutlined,
  FieldNumberOutlined,
  FieldTimeOutlined,
  FieldBinaryOutlined
} from '@ant-design/icons-vue'
import type { ReportField } from '../../types/report'

// 组件属性
interface Props {
  field: ReportField
  index?: number
  selected?: boolean
}

const props = defineProps<Props>()

// 组件事件
const emit = defineEmits<{
  configure: [field: ReportField]
  remove: [fieldId: string]
  duplicate: [field: ReportField]
  update: [field: ReportField]
  select: [field: ReportField]
}>()

// 响应式数据
const renameVisible = ref(false)
const newFieldName = ref('')
const newFieldLabel = ref('')

// 工具方法
const getFieldIcon = (dataType: string) => {
  switch (dataType) {
    case 'INTEGER':
    case 'DECIMAL':
      return 'FieldNumberOutlined'
    case 'DATE':
    case 'DATETIME':
      return 'FieldTimeOutlined'
    case 'BOOLEAN':
      return 'FieldBinaryOutlined'
    default:
      return 'FieldStringOutlined'
  }
}

const getFieldTypeColor = (fieldType: string) => {
  const colors = {
    dimension: 'green',
    measure: 'blue',
    filter: 'orange',
    calculated: 'purple'
  }
  return colors[fieldType] || 'default'
}

const getFieldTypeText = (fieldType: string) => {
  const texts = {
    dimension: '维度',
    measure: '度量',
    filter: '筛选',
    calculated: '计算'
  }
  return texts[fieldType] || fieldType
}

const getDataTypeText = (dataType: string) => {
  const texts = {
    STRING: '文本',
    INTEGER: '整数',
    DECIMAL: '小数',
    DATE: '日期',
    DATETIME: '日期时间',
    BOOLEAN: '布尔',
    JSON: 'JSON'
  }
  return texts[dataType] || dataType
}

const getAggregationText = (aggregation: string) => {
  const texts = {
    SUM: '求和',
    AVG: '平均',
    COUNT: '计数',
    MAX: '最大',
    MIN: '最小',
    COUNT_DISTINCT: '去重计数'
  }
  return texts[aggregation] || aggregation
}

// 事件处理
const handleFieldClick = () => {
  emit('select', props.field)
}

const handleMenuClick = ({ key }: { key: string }) => {
  switch (key) {
    case 'configure':
      emit('configure', props.field)
      break
    case 'rename':
      showRenameModal()
      break
    case 'duplicate':
      emit('duplicate', props.field)
      break
    case 'sort':
      toggleSort()
      break
    case 'filter':
      toggleFilter()
      break
    case 'visibility':
      toggleVisibility()
      break
    case 'remove':
      emit('remove', props.field.id)
      break
  }
}

const showRenameModal = () => {
  newFieldName.value = props.field.name
  newFieldLabel.value = props.field.label
  renameVisible.value = true
}

const handleRename = () => {
  if (!newFieldName.value.trim()) {
    message.error('字段名称不能为空')
    return
  }
  
  const updatedField: ReportField = {
    ...props.field,
    name: newFieldName.value.trim(),
    label: newFieldLabel.value.trim() || newFieldName.value.trim()
  }
  
  emit('update', updatedField)
  renameVisible.value = false
  message.success('字段重命名成功')
}

const toggleSort = () => {
  const updatedField: ReportField = {
    ...props.field,
    sort: {
      ...props.field.sort,
      enabled: !props.field.sort?.enabled
    }
  }
  
  emit('update', updatedField)
  message.success(`排序已${updatedField.sort?.enabled ? '启用' : '禁用'}`)
}

const toggleFilter = () => {
  const updatedField: ReportField = {
    ...props.field,
    filter: {
      ...props.field.filter,
      enabled: !props.field.filter?.enabled
    }
  }
  
  emit('update', updatedField)
  message.success(`筛选已${updatedField.filter?.enabled ? '启用' : '禁用'}`)
}

const toggleVisibility = () => {
  const updatedField: ReportField = {
    ...props.field,
    visible: !props.field.visible
  }
  
  emit('update', updatedField)
  message.success(`字段已${updatedField.visible ? '显示' : '隐藏'}`)
}
</script>

<style scoped lang="less">
.field-item {
  display: flex;
  align-items: center;
  padding: 8px;
  margin: 4px 0;
  background: #fff;
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  transition: all 0.2s;
  cursor: pointer;
  
  &:hover {
    border-color: #d9d9d9;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    
    .field-actions {
      opacity: 1;
    }
    
    .drag-handle {
      opacity: 1;
    }
  }
  
  &.field-selected {
    border-color: #1890ff;
    background: #f6ffed;
  }
  
  .field-content {
    flex: 1;
    display: flex;
    align-items: center;
    gap: 8px;
    min-width: 0;
    
    .field-icon {
      width: 24px;
      height: 24px;
      display: flex;
      align-items: center;
      justify-content: center;
      background: #f5f5f5;
      border-radius: 4px;
      font-size: 12px;
      color: #1890ff;
    }
    
    .field-info {
      flex: 1;
      min-width: 0;
      
      .field-name {
        font-size: 12px;
        font-weight: 500;
        color: #262626;
        margin-bottom: 2px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      
      .field-meta {
        display: flex;
        align-items: center;
        gap: 4px;
        
        .field-data-type {
          font-size: 10px;
          color: #8c8c8c;
        }
      }
    }
    
    .field-indicators {
      display: flex;
      align-items: center;
      gap: 4px;
      
      .indicator-icon {
        font-size: 12px;
        
        &.sort {
          color: #52c41a;
        }
        
        &.filter {
          color: #fa8c16;
        }
        
        &.format {
          color: #722ed1;
        }
        
        &.hidden {
          color: #bfbfbf;
        }
      }
    }
  }
  
  .field-actions {
    opacity: 0;
    transition: opacity 0.2s;
    
    .action-button {
      width: 20px;
      height: 20px;
      padding: 0;
      display: flex;
      align-items: center;
      justify-content: center;
      
      .anticon {
        font-size: 12px;
      }
    }
  }
  
  .drag-handle {
    opacity: 0;
    transition: opacity 0.2s;
    cursor: grab;
    padding: 2px;
    color: #bfbfbf;
    
    &:active {
      cursor: grabbing;
    }
    
    .anticon {
      font-size: 12px;
    }
  }
}

.danger-item {
  color: #ff4d4f !important;
}

// 标签样式覆盖
:deep(.ant-tag) {
  margin: 0;
  font-size: 10px;
  line-height: 16px;
  height: 16px;
  padding: 0 4px;
  border-radius: 2px;
}
</style>

<!--
  数据源面板
  @description 数据源选择和字段管理面板，支持拖拽字段到设计器
-->
<template>
  <div class="datasource-panel">
    <!-- 数据源选择 -->
    <div class="datasource-selector">
      <a-select
        v-model:value="selectedDataSourceId"
        placeholder="选择数据源"
        style="width: 100%"
        :loading="dataSourceLoading"
        @change="handleDataSourceChange"
      >
        <a-select-option
          v-for="ds in dataSources"
          :key="ds.id"
          :value="ds.id"
        >
          <div class="datasource-option">
            <component :is="getDataSourceIcon(ds.type)" />
            <span>{{ ds.name }}</span>
            <a-tag :color="getStatusColor(ds.status)" size="small">
              {{ getStatusText(ds.status) }}
            </a-tag>
          </div>
        </a-select-option>
      </a-select>
      
      <a-button
        type="link"
        size="small"
        @click="showDataSourceModal"
        style="margin-top: 8px; padding: 0;"
      >
        <PlusOutlined />
        新建数据源
      </a-button>
    </div>

    <!-- 数据源信息 -->
    <div v-if="currentDataSource" class="datasource-info">
      <a-descriptions size="small" :column="1">
        <a-descriptions-item label="类型">
          {{ getDataSourceTypeText(currentDataSource.type) }}
        </a-descriptions-item>
        <a-descriptions-item label="连接">
          {{ getConnectionString(currentDataSource) }}
        </a-descriptions-item>
        <a-descriptions-item label="状态">
          <a-badge
            :status="getStatusBadge(currentDataSource.status)"
            :text="getStatusText(currentDataSource.status)"
          />
        </a-descriptions-item>
      </a-descriptions>
      
      <div class="datasource-actions">
        <a-space size="small">
          <a-button size="small" @click="testConnection">
            <ApiOutlined />
            测试连接
          </a-button>
          <a-button size="small" @click="refreshMetadata">
            <ReloadOutlined />
            刷新
          </a-button>
        </a-space>
      </div>
    </div>

    <!-- 表和字段树 -->
    <div v-if="metadata" class="metadata-tree">
      <a-input
        v-model:value="searchKeyword"
        placeholder="搜索表或字段..."
        allow-clear
        size="small"
        style="margin-bottom: 8px"
      >
        <template #prefix>
          <SearchOutlined />
        </template>
      </a-input>
      
      <a-tree
        v-model:expandedKeys="expandedKeys"
        :tree-data="filteredTreeData"
        :show-icon="true"
        :draggable="true"
        block-node
        @dragstart="handleFieldDragStart"
        @dragend="handleFieldDragEnd"
        @select="handleNodeSelect"
      >
        <template #icon="{ dataRef }">
          <component :is="getNodeIcon(dataRef)" />
        </template>
        
        <template #title="{ dataRef }">
          <div class="tree-node-title">
            <span class="node-name">{{ dataRef.title }}</span>
            <span v-if="dataRef.type" class="node-type">{{ dataRef.type }}</span>
            <span v-if="dataRef.comment" class="node-comment">{{ dataRef.comment }}</span>
          </div>
        </template>
      </a-tree>
    </div>

    <!-- 加载状态 -->
    <div v-if="metadataLoading" class="loading-state">
      <a-spin>
        <div style="height: 100px;"></div>
      </a-spin>
    </div>

    <!-- 空状态 -->
    <div v-if="!selectedDataSourceId && !dataSourceLoading" class="empty-state">
      <a-empty
        description="请选择数据源"
        :image="Empty.PRESENTED_IMAGE_SIMPLE"
      />
    </div>

    <!-- 字段详情弹窗 -->
    <a-modal
      v-model:visible="fieldDetailVisible"
      :title="currentField?.title"
      width="600px"
      :footer="null"
    >
      <div v-if="currentField" class="field-detail">
        <a-descriptions :column="2" bordered>
          <a-descriptions-item label="字段名">{{ currentField.name }}</a-descriptions-item>
          <a-descriptions-item label="数据类型">{{ currentField.type }}</a-descriptions-item>
          <a-descriptions-item label="是否可空">
            {{ currentField.nullable ? '是' : '否' }}
          </a-descriptions-item>
          <a-descriptions-item label="是否主键">
            {{ currentField.primaryKey ? '是' : '否' }}
          </a-descriptions-item>
          <a-descriptions-item label="默认值" :span="2">
            {{ currentField.defaultValue || '-' }}
          </a-descriptions-item>
          <a-descriptions-item label="注释" :span="2">
            {{ currentField.comment || '-' }}
          </a-descriptions-item>
        </a-descriptions>
        
        <!-- 字段统计信息 -->
        <div v-if="fieldStats" class="field-stats">
          <h4>字段统计</h4>
          <a-row :gutter="16">
            <a-col :span="8">
              <a-statistic title="总记录数" :value="fieldStats.totalCount" />
            </a-col>
            <a-col :span="8">
              <a-statistic title="非空记录" :value="fieldStats.nonNullCount" />
            </a-col>
            <a-col :span="8">
              <a-statistic title="唯一值数" :value="fieldStats.distinctCount" />
            </a-col>
          </a-row>
        </div>
        
        <!-- 字段预览 -->
        <div v-if="fieldPreview" class="field-preview">
          <h4>数据预览</h4>
          <a-table
            :columns="previewColumns"
            :data-source="fieldPreview"
            :pagination="false"
            size="small"
            :scroll="{ y: 200 }"
          />
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import { message, Empty } from 'ant-design-vue'
import {
  PlusOutlined,
  ApiOutlined,
  ReloadOutlined,
  SearchOutlined,
  DatabaseOutlined,
  TableOutlined,
  FieldStringOutlined,
  FieldNumberOutlined,
  FieldTimeOutlined,
  FieldBinaryOutlined
} from '@ant-design/icons-vue'
import { useDataSourceStore } from '../../stores/datasource'
import type { DataSourceConfig, DataSourceMetadata, TableMetadata, ColumnMetadata } from '../../types/datasource'

// 组件属性
interface Props {
  selected?: string
}

const props = defineProps<Props>()

// 组件事件
const emit = defineEmits<{
  'update:selected': [value: string]
  fieldDragStart: [field: ColumnMetadata, event: DragEvent]
  fieldDragEnd: []
  fieldSelect: [field: ColumnMetadata]
}>()

// 状态管理
const dataSourceStore = useDataSourceStore()
const { dataSources, loading: dataSourceLoading } = storeToRefs(dataSourceStore)

// 响应式数据
const selectedDataSourceId = ref(props.selected || '')
const currentDataSource = ref<DataSourceConfig | null>(null)
const metadata = ref<DataSourceMetadata | null>(null)
const metadataLoading = ref(false)
const searchKeyword = ref('')
const expandedKeys = ref<string[]>([])
const fieldDetailVisible = ref(false)
const currentField = ref<ColumnMetadata | null>(null)
const fieldStats = ref<any>(null)
const fieldPreview = ref<any[]>([])

// 预览表格列
const previewColumns = [
  { title: '值', dataIndex: 'value', key: 'value' },
  { title: '频次', dataIndex: 'count', key: 'count' }
]

// 计算属性
const filteredTreeData = computed(() => {
  if (!metadata.value || !searchKeyword.value) {
    return treeData.value
  }
  
  const keyword = searchKeyword.value.toLowerCase()
  return treeData.value
    .map(table => ({
      ...table,
      children: table.children?.filter(field =>
        field.title.toLowerCase().includes(keyword) ||
        field.comment?.toLowerCase().includes(keyword)
      )
    }))
    .filter(table =>
      table.title.toLowerCase().includes(keyword) ||
      (table.children && table.children.length > 0)
    )
})

const treeData = computed(() => {
  if (!metadata.value) return []
  
  return metadata.value.tables.map(table => ({
    key: `table_${table.name}`,
    title: table.name,
    icon: 'TableOutlined',
    type: 'table',
    comment: table.comment,
    selectable: false,
    children: table.columns.map(column => ({
      key: `field_${table.name}_${column.name}`,
      title: column.name,
      icon: getFieldIcon(column.dataType),
      type: column.dataType,
      comment: column.comment,
      isLeaf: true,
      draggable: true,
      ...column
    }))
  }))
})

// 工具方法
const getDataSourceIcon = (type: string) => {
  const icons = {
    MYSQL: 'DatabaseOutlined',
    POSTGRESQL: 'DatabaseOutlined',
    ORACLE: 'DatabaseOutlined',
    MONGODB: 'DatabaseOutlined',
    REST_API: 'ApiOutlined'
  }
  return icons[type] || 'DatabaseOutlined'
}

const getStatusColor = (status: string) => {
  const colors = {
    ACTIVE: 'green',
    DISABLED: 'default',
    CONNECTION_FAILED: 'red',
    MAINTENANCE: 'orange'
  }
  return colors[status] || 'default'
}

const getStatusText = (status: string) => {
  const texts = {
    ACTIVE: '正常',
    DISABLED: '禁用',
    CONNECTION_FAILED: '连接失败',
    MAINTENANCE: '维护中'
  }
  return texts[status] || status
}

const getStatusBadge = (status: string) => {
  const badges = {
    ACTIVE: 'success',
    DISABLED: 'default',
    CONNECTION_FAILED: 'error',
    MAINTENANCE: 'warning'
  }
  return badges[status] || 'default'
}

const getDataSourceTypeText = (type: string) => {
  const texts = {
    MYSQL: 'MySQL数据库',
    POSTGRESQL: 'PostgreSQL数据库',
    ORACLE: 'Oracle数据库',
    MONGODB: 'MongoDB数据库',
    REST_API: 'REST API'
  }
  return texts[type] || type
}

const getConnectionString = (ds: DataSourceConfig) => {
  const { connection } = ds
  if (connection.host && connection.port) {
    return `${connection.host}:${connection.port}`
  }
  return connection.url || '-'
}

const getNodeIcon = (node: any) => {
  if (node.type === 'table') {
    return 'TableOutlined'
  }
  return getFieldIcon(node.type)
}

const getFieldIcon = (dataType: string) => {
  const type = dataType?.toLowerCase() || ''
  if (type.includes('int') || type.includes('decimal') || type.includes('float')) {
    return 'FieldNumberOutlined'
  }
  if (type.includes('date') || type.includes('time')) {
    return 'FieldTimeOutlined'
  }
  if (type.includes('blob') || type.includes('binary')) {
    return 'FieldBinaryOutlined'
  }
  return 'FieldStringOutlined'
}

// 事件处理
const handleDataSourceChange = async (dataSourceId: string) => {
  selectedDataSourceId.value = dataSourceId
  emit('update:selected', dataSourceId)
  
  // 获取数据源详情
  currentDataSource.value = dataSources.value.find(ds => ds.id === dataSourceId) || null
  
  // 加载元数据
  if (currentDataSource.value) {
    await loadMetadata()
  }
}

const loadMetadata = async () => {
  if (!selectedDataSourceId.value) return
  
  try {
    metadataLoading.value = true
    metadata.value = await dataSourceStore.getMetadata(selectedDataSourceId.value)
    
    // 默认展开第一个表
    if (metadata.value?.tables.length > 0) {
      expandedKeys.value = [`table_${metadata.value.tables[0].name}`]
    }
  } catch (error) {
    message.error('加载元数据失败')
  } finally {
    metadataLoading.value = false
  }
}

const testConnection = async () => {
  if (!selectedDataSourceId.value) return
  
  try {
    const result = await dataSourceStore.testConnection(selectedDataSourceId.value)
    if (result.status === 'CONNECTED') {
      message.success('连接测试成功')
    } else {
      message.error(`连接测试失败: ${result.errorMessage}`)
    }
  } catch (error) {
    message.error('连接测试失败')
  }
}

const refreshMetadata = async () => {
  if (!selectedDataSourceId.value) return
  
  try {
    await dataSourceStore.refreshMetadata(selectedDataSourceId.value)
    await loadMetadata()
    message.success('元数据刷新成功')
  } catch (error) {
    message.error('元数据刷新失败')
  }
}

const showDataSourceModal = () => {
  // 显示新建数据源弹窗
  // 这里可以触发父组件的事件或者使用全局状态
}

const handleFieldDragStart = (info: any) => {
  const { node, event } = info
  if (node.isLeaf && node.draggable) {
    // 设置拖拽数据
    event.dataTransfer.setData('application/json', JSON.stringify({
      type: 'field',
      data: node
    }))
    event.dataTransfer.effectAllowed = 'copy'
    
    emit('fieldDragStart', node, event)
  }
}

const handleFieldDragEnd = () => {
  emit('fieldDragEnd')
}

const handleNodeSelect = async (selectedKeys: string[], info: any) => {
  const { node } = info
  if (node.isLeaf) {
    currentField.value = node
    
    // 加载字段统计和预览数据
    await loadFieldDetails(node)
    
    fieldDetailVisible.value = true
    emit('fieldSelect', node)
  }
}

const loadFieldDetails = async (field: ColumnMetadata) => {
  try {
    // 这里可以调用API获取字段的统计信息和预览数据
    // fieldStats.value = await dataSourceStore.getFieldStats(selectedDataSourceId.value, field)
    // fieldPreview.value = await dataSourceStore.getFieldPreview(selectedDataSourceId.value, field)
    
    // 模拟数据
    fieldStats.value = {
      totalCount: 1000,
      nonNullCount: 950,
      distinctCount: 800
    }
    
    fieldPreview.value = [
      { value: 'Sample Value 1', count: 150 },
      { value: 'Sample Value 2', count: 120 },
      { value: 'Sample Value 3', count: 100 }
    ]
  } catch (error) {
    console.error('加载字段详情失败:', error)
  }
}

// 监听属性变化
watch(() => props.selected, (newValue) => {
  if (newValue !== selectedDataSourceId.value) {
    selectedDataSourceId.value = newValue || ''
    if (newValue) {
      handleDataSourceChange(newValue)
    }
  }
})

// 生命周期
onMounted(() => {
  dataSourceStore.fetchDataSources()
  
  if (selectedDataSourceId.value) {
    handleDataSourceChange(selectedDataSourceId.value)
  }
})
</script>

<style scoped lang="less">
.datasource-panel {
  height: 100%;
  display: flex;
  flex-direction: column;
  
  .datasource-selector {
    padding: 12px;
    border-bottom: 1px solid #f0f0f0;
    
    .datasource-option {
      display: flex;
      align-items: center;
      gap: 8px;
    }
  }
  
  .datasource-info {
    padding: 12px;
    border-bottom: 1px solid #f0f0f0;
    background: #fafafa;
    
    .datasource-actions {
      margin-top: 8px;
    }
  }
  
  .metadata-tree {
    flex: 1;
    padding: 12px;
    overflow-y: auto;
    
    .tree-node-title {
      display: flex;
      align-items: center;
      gap: 8px;
      
      .node-name {
        font-weight: 500;
        color: #262626;
      }
      
      .node-type {
        font-size: 11px;
        color: #8c8c8c;
        background: #f0f0f0;
        padding: 1px 4px;
        border-radius: 2px;
      }
      
      .node-comment {
        font-size: 11px;
        color: #8c8c8c;
        font-style: italic;
      }
    }
  }
  
  .loading-state,
  .empty-state {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  .field-detail {
    .field-stats,
    .field-preview {
      margin-top: 16px;
      
      h4 {
        margin-bottom: 12px;
        font-size: 14px;
        font-weight: 600;
        color: #262626;
      }
    }
  }
}

// 树组件样式覆盖
:deep(.ant-tree) {
  .ant-tree-node-content-wrapper {
    &:hover {
      background-color: #f5f5f5;
    }
    
    &.ant-tree-node-selected {
      background-color: #e6f7ff;
    }
  }
  
  .ant-tree-draggable-icon {
    display: none;
  }
  
  .ant-tree-treenode[draggable="true"] {
    cursor: grab;
    
    &:active {
      cursor: grabbing;
    }
  }
}
</style>

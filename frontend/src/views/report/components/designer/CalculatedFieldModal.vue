<!--
  计算字段弹窗
  @description 创建和编辑计算字段的弹窗组件
-->
<template>
  <a-modal
    v-model:visible="modalVisible"
    title="计算字段"
    width="800px"
    :ok-button-props="{ loading: saving }"
    @ok="handleSave"
    @cancel="handleCancel"
  >
    <div class="calculated-field-modal">
      <a-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        layout="vertical"
      >
        <!-- 基本信息 -->
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="字段名称" name="name">
              <a-input
                v-model:value="formData.name"
                placeholder="请输入字段名称"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="显示标签" name="label">
              <a-input
                v-model:value="formData.label"
                placeholder="请输入显示标签"
              />
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16">
          <a-col :span="8">
            <a-form-item label="数据类型" name="dataType">
              <a-select v-model:value="formData.dataType" placeholder="选择数据类型">
                <a-select-option value="STRING">文本</a-select-option>
                <a-select-option value="INTEGER">整数</a-select-option>
                <a-select-option value="DECIMAL">小数</a-select-option>
                <a-select-option value="DATE">日期</a-select-option>
                <a-select-option value="BOOLEAN">布尔</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="字段类型" name="fieldType">
              <a-select v-model:value="formData.fieldType" placeholder="选择字段类型">
                <a-select-option value="dimension">维度</a-select-option>
                <a-select-option value="measure">度量</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item v-if="formData.fieldType === 'measure'" label="聚合方式" name="aggregation">
              <a-select v-model:value="formData.aggregation" placeholder="选择聚合方式">
                <a-select-option value="SUM">求和</a-select-option>
                <a-select-option value="AVG">平均值</a-select-option>
                <a-select-option value="COUNT">计数</a-select-option>
                <a-select-option value="MAX">最大值</a-select-option>
                <a-select-option value="MIN">最小值</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 计算公式 -->
        <a-form-item label="计算公式" name="formula">
          <div class="formula-editor">
            <div class="editor-toolbar">
              <a-space>
                <a-button size="small" @click="insertFunction('SUM')">SUM</a-button>
                <a-button size="small" @click="insertFunction('AVG')">AVG</a-button>
                <a-button size="small" @click="insertFunction('COUNT')">COUNT</a-button>
                <a-button size="small" @click="insertFunction('IF')">IF</a-button>
                <a-button size="small" @click="insertOperator('+')">+</a-button>
                <a-button size="small" @click="insertOperator('-')">-</a-button>
                <a-button size="small" @click="insertOperator('*')">×</a-button>
                <a-button size="small" @click="insertOperator('/')">/</a-button>
                <a-button size="small" @click="insertOperator('(')">(</a-button>
                <a-button size="small" @click="insertOperator(')')">)</a-button>
              </a-space>
            </div>
            <a-textarea
              ref="formulaEditor"
              v-model:value="formData.formula"
              placeholder="请输入计算公式，例如: [销售额] * [数量]"
              :rows="6"
              @change="validateFormula"
            />
            <div v-if="formulaError" class="formula-error">
              <ExclamationCircleOutlined />
              {{ formulaError }}
            </div>
          </div>
        </a-form-item>

        <!-- 可用字段 -->
        <a-form-item label="可用字段">
          <div class="available-fields">
            <a-input
              v-model:value="fieldSearchKeyword"
              placeholder="搜索字段..."
              size="small"
              style="margin-bottom: 8px"
            >
              <template #prefix>
                <SearchOutlined />
              </template>
            </a-input>
            <div class="field-list">
              <div
                v-for="field in filteredAvailableFields"
                :key="field.id"
                class="field-item"
                @click="insertField(field)"
              >
                <component :is="getFieldIcon(field.dataType)" />
                <span class="field-name">{{ field.label || field.name }}</span>
                <a-tag :color="getFieldTypeColor(field.fieldType)" size="small">
                  {{ getFieldTypeText(field.fieldType) }}
                </a-tag>
              </div>
            </div>
          </div>
        </a-form-item>

        <!-- 函数说明 -->
        <a-form-item label="函数说明">
          <a-collapse size="small">
            <a-collapse-panel key="math" header="数学函数">
              <div class="function-help">
                <div class="function-item">
                  <strong>SUM([字段])</strong> - 求和
                </div>
                <div class="function-item">
                  <strong>AVG([字段])</strong> - 平均值
                </div>
                <div class="function-item">
                  <strong>COUNT([字段])</strong> - 计数
                </div>
                <div class="function-item">
                  <strong>MAX([字段])</strong> - 最大值
                </div>
                <div class="function-item">
                  <strong>MIN([字段])</strong> - 最小值
                </div>
              </div>
            </a-collapse-panel>
            <a-collapse-panel key="logic" header="逻辑函数">
              <div class="function-help">
                <div class="function-item">
                  <strong>IF(条件, 值1, 值2)</strong> - 条件判断
                </div>
                <div class="function-item">
                  <strong>AND(条件1, 条件2)</strong> - 逻辑与
                </div>
                <div class="function-item">
                  <strong>OR(条件1, 条件2)</strong> - 逻辑或
                </div>
              </div>
            </a-collapse-panel>
            <a-collapse-panel key="text" header="文本函数">
              <div class="function-help">
                <div class="function-item">
                  <strong>CONCAT([字段1], [字段2])</strong> - 文本连接
                </div>
                <div class="function-item">
                  <strong>LEFT([字段], 长度)</strong> - 左侧截取
                </div>
                <div class="function-item">
                  <strong>RIGHT([字段], 长度)</strong> - 右侧截取
                </div>
              </div>
            </a-collapse-panel>
          </a-collapse>
        </a-form-item>
      </a-form>
    </div>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, nextTick } from 'vue'
import { message } from 'ant-design-vue'
import {
  ExclamationCircleOutlined,
  SearchOutlined,
  FieldStringOutlined,
  FieldNumberOutlined,
  FieldTimeOutlined,
  FieldBinaryOutlined
} from '@ant-design/icons-vue'
import type { ReportField } from '../../types/report'
import { generateId } from '../../utils/report-utils'

// 组件属性
interface Props {
  visible: boolean
  availableFields: ReportField[]
  editField?: ReportField | null
}

const props = withDefaults(defineProps<Props>(), {
  editField: null
})

// 组件事件
const emit = defineEmits<{
  'update:visible': [visible: boolean]
  save: [field: ReportField]
}>()

// 响应式数据
const modalVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

const formRef = ref()
const formulaEditor = ref()
const saving = ref(false)
const formulaError = ref('')
const fieldSearchKeyword = ref('')

const formData = reactive({
  name: '',
  label: '',
  dataType: 'STRING',
  fieldType: 'dimension',
  aggregation: undefined,
  formula: ''
})

const formRules = {
  name: [{ required: true, message: '请输入字段名称' }],
  label: [{ required: true, message: '请输入显示标签' }],
  dataType: [{ required: true, message: '请选择数据类型' }],
  fieldType: [{ required: true, message: '请选择字段类型' }],
  formula: [{ required: true, message: '请输入计算公式' }]
}

// 计算属性
const filteredAvailableFields = computed(() => {
  if (!fieldSearchKeyword.value) {
    return props.availableFields
  }
  
  const keyword = fieldSearchKeyword.value.toLowerCase()
  return props.availableFields.filter(field =>
    field.name.toLowerCase().includes(keyword) ||
    field.label.toLowerCase().includes(keyword)
  )
})

// 工具方法
const getFieldIcon = (dataType: string) => {
  switch (dataType) {
    case 'INTEGER':
    case 'DECIMAL':
      return 'FieldNumberOutlined'
    case 'DATE':
    case 'DATETIME':
      return 'FieldTimeOutlined'
    case 'BOOLEAN':
      return 'FieldBinaryOutlined'
    default:
      return 'FieldStringOutlined'
  }
}

const getFieldTypeColor = (fieldType: string) => {
  const colors = {
    dimension: 'green',
    measure: 'blue',
    filter: 'orange',
    calculated: 'purple'
  }
  return colors[fieldType] || 'default'
}

const getFieldTypeText = (fieldType: string) => {
  const texts = {
    dimension: '维度',
    measure: '度量',
    filter: '筛选',
    calculated: '计算'
  }
  return texts[fieldType] || fieldType
}

// 事件处理
const insertFunction = (func: string) => {
  const textarea = formulaEditor.value?.$el?.querySelector('textarea')
  if (textarea) {
    const start = textarea.selectionStart
    const end = textarea.selectionEnd
    const text = formData.formula
    
    let insertText = ''
    switch (func) {
      case 'IF':
        insertText = 'IF(, , )'
        break
      default:
        insertText = `${func}()`
    }
    
    formData.formula = text.substring(0, start) + insertText + text.substring(end)
    
    nextTick(() => {
      textarea.focus()
      const newPos = start + insertText.length - 1
      textarea.setSelectionRange(newPos, newPos)
    })
  }
}

const insertOperator = (operator: string) => {
  const textarea = formulaEditor.value?.$el?.querySelector('textarea')
  if (textarea) {
    const start = textarea.selectionStart
    const end = textarea.selectionEnd
    const text = formData.formula
    
    formData.formula = text.substring(0, start) + operator + text.substring(end)
    
    nextTick(() => {
      textarea.focus()
      const newPos = start + operator.length
      textarea.setSelectionRange(newPos, newPos)
    })
  }
}

const insertField = (field: ReportField) => {
  const textarea = formulaEditor.value?.$el?.querySelector('textarea')
  if (textarea) {
    const start = textarea.selectionStart
    const end = textarea.selectionEnd
    const text = formData.formula
    const fieldRef = `[${field.label || field.name}]`
    
    formData.formula = text.substring(0, start) + fieldRef + text.substring(end)
    
    nextTick(() => {
      textarea.focus()
      const newPos = start + fieldRef.length
      textarea.setSelectionRange(newPos, newPos)
    })
  }
}

const validateFormula = () => {
  formulaError.value = ''
  
  if (!formData.formula.trim()) {
    return
  }
  
  try {
    // 简单的语法检查
    const formula = formData.formula
    
    // 检查括号匹配
    let openCount = 0
    for (const char of formula) {
      if (char === '(') openCount++
      if (char === ')') openCount--
      if (openCount < 0) {
        formulaError.value = '括号不匹配'
        return
      }
    }
    
    if (openCount !== 0) {
      formulaError.value = '括号不匹配'
      return
    }
    
    // 检查字段引用格式
    const fieldRefs = formula.match(/\[([^\]]+)\]/g)
    if (fieldRefs) {
      for (const ref of fieldRefs) {
        const fieldName = ref.slice(1, -1)
        const field = props.availableFields.find(f => 
          f.name === fieldName || f.label === fieldName
        )
        if (!field) {
          formulaError.value = `未找到字段: ${fieldName}`
          return
        }
      }
    }
    
  } catch (error) {
    formulaError.value = '公式语法错误'
  }
}

const handleSave = async () => {
  try {
    await formRef.value.validate()
    
    if (formulaError.value) {
      message.error('请修正公式错误')
      return
    }
    
    saving.value = true
    
    const calculatedField: ReportField = {
      id: props.editField?.id || generateId('calc_'),
      name: formData.name,
      label: formData.label,
      dataType: formData.dataType as any,
      fieldType: formData.fieldType as any,
      aggregation: formData.fieldType === 'measure' ? formData.aggregation as any : undefined,
      required: false,
      visible: true,
      isCalculated: true,
      formula: formData.formula,
      sort: { enabled: false },
      filter: { enabled: false }
    }
    
    emit('save', calculatedField)
    handleCancel()
    
  } catch (error) {
    console.error('保存计算字段失败:', error)
  } finally {
    saving.value = false
  }
}

const handleCancel = () => {
  modalVisible.value = false
  resetForm()
}

const resetForm = () => {
  Object.assign(formData, {
    name: '',
    label: '',
    dataType: 'STRING',
    fieldType: 'dimension',
    aggregation: undefined,
    formula: ''
  })
  formulaError.value = ''
  fieldSearchKeyword.value = ''
}

// 监听编辑字段变化
watch(() => props.editField, (field) => {
  if (field) {
    Object.assign(formData, {
      name: field.name,
      label: field.label,
      dataType: field.dataType,
      fieldType: field.fieldType,
      aggregation: field.aggregation,
      formula: field.formula || ''
    })
  }
}, { immediate: true })

// 监听弹窗显示状态
watch(modalVisible, (visible) => {
  if (!visible) {
    resetForm()
  }
})
</script>

<style scoped lang="less">
.calculated-field-modal {
  .formula-editor {
    .editor-toolbar {
      margin-bottom: 8px;
      padding: 8px;
      background: #fafafa;
      border-radius: 6px;
    }
    
    .formula-error {
      margin-top: 8px;
      padding: 8px;
      background: #fff2f0;
      border: 1px solid #ffccc7;
      border-radius: 4px;
      color: #ff4d4f;
      font-size: 12px;
      display: flex;
      align-items: center;
      gap: 4px;
    }
  }
  
  .available-fields {
    .field-list {
      max-height: 200px;
      overflow-y: auto;
      border: 1px solid #f0f0f0;
      border-radius: 6px;
      
      .field-item {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 8px 12px;
        cursor: pointer;
        transition: background-color 0.2s;
        
        &:hover {
          background: #f5f5f5;
        }
        
        &:not(:last-child) {
          border-bottom: 1px solid #f0f0f0;
        }
        
        .field-name {
          flex: 1;
          font-size: 12px;
          color: #262626;
        }
      }
    }
  }
  
  .function-help {
    .function-item {
      margin-bottom: 8px;
      font-size: 12px;
      line-height: 1.5;
      
      strong {
        color: #1890ff;
        font-family: 'Monaco', 'Menlo', monospace;
      }
    }
  }
}
</style>

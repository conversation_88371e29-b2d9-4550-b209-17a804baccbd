/**
 * FieldPanel 组件单元测试
 */

import { describe, it, expect, vi, beforeEach } from 'vitest'
import { mount } from '@vue/test-utils'
import { createPinia, setActivePinia } from 'pinia'
import FieldPanel from '../FieldPanel.vue'
import type { ReportField } from '../../../types/report'

// Mock Ant Design Vue 组件
vi.mock('ant-design-vue', () => ({
  message: {
    success: vi.fn(),
    error: vi.fn(),
    warning: vi.fn()
  }
}))

// Mock vuedraggable
vi.mock('vuedraggable', () => ({
  default: {
    name: 'draggable',
    template: '<div><slot /></div>',
    props: ['modelValue', 'group', 'itemKey', 'animation'],
    emits: ['update:modelValue', 'change']
  }
}))

describe('FieldPanel', () => {
  const mockFields: ReportField[] = [
    {
      id: 'field1',
      name: 'sales',
      label: '销售额',
      dataType: 'DECIMAL',
      fieldType: 'measure',
      required: false,
      visible: true,
      aggregation: 'SUM',
      sort: { enabled: false },
      filter: { enabled: false }
    },
    {
      id: 'field2',
      name: 'category',
      label: '分类',
      dataType: 'STRING',
      fieldType: 'dimension',
      required: false,
      visible: true,
      sort: { enabled: false },
      filter: { enabled: false }
    },
    {
      id: 'field3',
      name: 'date',
      label: '日期',
      dataType: 'DATE',
      fieldType: 'filter',
      required: false,
      visible: true,
      sort: { enabled: false },
      filter: { enabled: true }
    }
  ]

  beforeEach(() => {
    setActivePinia(createPinia())
  })

  it('应该正确渲染字段面板', () => {
    const wrapper = mount(FieldPanel, {
      props: {
        fields: mockFields
      }
    })
    
    // 检查操作按钮是否存在
    expect(wrapper.find('button').exists()).toBe(true)
    
    // 检查字段分组是否存在
    expect(wrapper.find('.field-group').exists()).toBe(true)
  })

  it('应该正确分组字段', () => {
    const wrapper = mount(FieldPanel, {
      props: {
        fields: mockFields
      }
    })
    
    // 验证维度字段
    const dimensionFields = wrapper.vm.dimensionFields
    expect(dimensionFields.length).toBe(1)
    expect(dimensionFields[0].fieldType).toBe('dimension')
    
    // 验证度量字段
    const measureFields = wrapper.vm.measureFields
    expect(measureFields.length).toBe(1)
    expect(measureFields[0].fieldType).toBe('measure')
    
    // 验证筛选字段
    const filterFields = wrapper.vm.filterFields
    expect(filterFields.length).toBe(1)
    expect(filterFields[0].fieldType).toBe('filter')
  })

  it('应该正确计算字段总数', () => {
    const wrapper = mount(FieldPanel, {
      props: {
        fields: mockFields
      }
    })
    
    expect(wrapper.vm.totalFieldCount).toBe(3)
  })

  it('应该正确处理字段拖放', async () => {
    const wrapper = mount(FieldPanel, {
      props: {
        fields: []
      }
    })
    
    // 模拟拖放事件
    const mockEvent = {
      preventDefault: vi.fn(),
      dataTransfer: {
        getData: vi.fn().mockReturnValue(JSON.stringify({
          type: 'field',
          data: {
            name: 'newField',
            title: '新字段',
            type: 'varchar'
          }
        }))
      }
    } as any
    
    wrapper.vm.handleFieldDrop('dimension', mockEvent)
    
    // 验证事件处理
    expect(mockEvent.preventDefault).toHaveBeenCalled()
    expect(wrapper.emitted('update:fields')).toBeTruthy()
  })

  it('应该正确处理字段移除', () => {
    const wrapper = mount(FieldPanel, {
      props: {
        fields: mockFields
      }
    })
    
    wrapper.vm.handleFieldRemove('field1')
    
    // 验证字段移除事件
    expect(wrapper.emitted('update:fields')).toBeTruthy()
    expect(wrapper.emitted('fieldRemove')).toBeTruthy()
    expect(wrapper.emitted('fieldRemove')?.[0]).toEqual(['field1'])
  })

  it('应该正确处理字段配置', () => {
    const wrapper = mount(FieldPanel, {
      props: {
        fields: mockFields
      }
    })
    
    const field = mockFields[0]
    wrapper.vm.handleFieldConfigure(field)
    
    // 验证配置事件
    expect(wrapper.emitted('fieldConfigure')).toBeTruthy()
    expect(wrapper.emitted('fieldConfigure')?.[0]).toEqual([field])
  })

  it('应该正确处理字段复制', () => {
    const wrapper = mount(FieldPanel, {
      props: {
        fields: mockFields
      }
    })
    
    const field = mockFields[0]
    wrapper.vm.handleFieldDuplicate(field)
    
    // 验证复制事件
    expect(wrapper.emitted('update:fields')).toBeTruthy()
    
    const emittedFields = wrapper.emitted('update:fields')?.[0]?.[0] as ReportField[]
    expect(emittedFields.length).toBe(mockFields.length + 1)
    
    // 验证复制的字段
    const duplicatedField = emittedFields.find(f => f.name === 'sales_copy')
    expect(duplicatedField).toBeDefined()
    expect(duplicatedField?.label).toBe('销售额_副本')
  })

  it('应该正确处理计算字段添加', () => {
    const wrapper = mount(FieldPanel, {
      props: {
        fields: mockFields
      }
    })
    
    wrapper.vm.addCalculatedField()
    
    // 验证弹窗状态
    expect(wrapper.vm.calculatedFieldVisible).toBe(true)
  })

  it('应该正确处理计算字段保存', () => {
    const wrapper = mount(FieldPanel, {
      props: {
        fields: mockFields
      }
    })
    
    const calculatedField: ReportField = {
      id: 'calc1',
      name: 'profit',
      label: '利润',
      dataType: 'DECIMAL',
      fieldType: 'measure',
      required: false,
      visible: true,
      isCalculated: true,
      formula: '[销售额] - [成本]',
      sort: { enabled: false },
      filter: { enabled: false }
    }
    
    wrapper.vm.handleCalculatedFieldSave(calculatedField)
    
    // 验证字段添加
    expect(wrapper.emitted('update:fields')).toBeTruthy()
    expect(wrapper.vm.calculatedFieldVisible).toBe(false)
  })

  it('应该正确处理清空所有字段', () => {
    const wrapper = mount(FieldPanel, {
      props: {
        fields: mockFields
      }
    })
    
    wrapper.vm.clearAllFields()
    
    // 验证清空事件
    expect(wrapper.emitted('update:fields')).toBeTruthy()
    expect(wrapper.emitted('update:fields')?.[0]).toEqual([[]])
  })

  it('应该正确处理拖拽区域状态', () => {
    const wrapper = mount(FieldPanel, {
      props: {
        fields: mockFields
      }
    })
    
    // 测试拖拽进入
    const mockEvent = {
      preventDefault: vi.fn()
    } as any
    
    wrapper.vm.handleDragOver('dimension', mockEvent)
    expect(wrapper.vm.dragOverZone).toBe('dimension')
    expect(mockEvent.preventDefault).toHaveBeenCalled()
    
    // 测试拖拽离开
    wrapper.vm.handleDragLeave()
    expect(wrapper.vm.dragOverZone).toBeNull()
  })

  it('应该正确映射数据类型', () => {
    const wrapper = mount(FieldPanel, {
      props: {
        fields: []
      }
    })
    
    // 测试数字类型映射
    expect(wrapper.vm.mapDataType('int')).toBe('INTEGER')
    expect(wrapper.vm.mapDataType('decimal')).toBe('INTEGER')
    expect(wrapper.vm.mapDataType('float')).toBe('INTEGER')
    
    // 测试日期类型映射
    expect(wrapper.vm.mapDataType('date')).toBe('DATE')
    expect(wrapper.vm.mapDataType('datetime')).toBe('DATE')
    
    // 测试字符串类型映射
    expect(wrapper.vm.mapDataType('varchar')).toBe('STRING')
    expect(wrapper.vm.mapDataType('text')).toBe('STRING')
  })

  it('应该正确获取默认格式', () => {
    const wrapper = mount(FieldPanel, {
      props: {
        fields: []
      }
    })
    
    // 测试数字格式
    const numberFormat = wrapper.vm.getDefaultFormat('int')
    expect(numberFormat.type).toBe('number')
    expect(numberFormat.decimals).toBe(2)
    expect(numberFormat.thousandSeparator).toBe(true)
    
    // 测试日期格式
    const dateFormat = wrapper.vm.getDefaultFormat('date')
    expect(dateFormat.type).toBe('date')
    expect(dateFormat.dateFormat).toBe('YYYY-MM-DD')
    
    // 测试自定义格式
    const customFormat = wrapper.vm.getDefaultFormat('varchar')
    expect(customFormat.type).toBe('custom')
  })

  it('应该正确处理字段顺序变化', () => {
    const wrapper = mount(FieldPanel, {
      props: {
        fields: mockFields
      }
    })
    
    wrapper.vm.handleFieldOrderChange()
    
    // 验证字段更新事件
    expect(wrapper.emitted('update:fields')).toBeTruthy()
  })

  it('应该正确处理重复字段检查', () => {
    const wrapper = mount(FieldPanel, {
      props: {
        fields: mockFields
      }
    })
    
    // 模拟添加重复字段
    const mockEvent = {
      preventDefault: vi.fn(),
      dataTransfer: {
        getData: vi.fn().mockReturnValue(JSON.stringify({
          type: 'field',
          data: {
            name: 'sales', // 已存在的字段名
            title: '销售额',
            type: 'decimal'
          }
        }))
      }
    } as any
    
    wrapper.vm.handleFieldDrop('measure', mockEvent)
    
    // 验证不会添加重复字段
    expect(wrapper.emitted('update:fields')).toBeFalsy()
  })
})

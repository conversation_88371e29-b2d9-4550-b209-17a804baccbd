/**
 * ComponentPanel 组件单元测试
 */

import { describe, it, expect, vi, beforeEach } from 'vitest'
import { mount } from '@vue/test-utils'
import { createPinia, setActivePinia } from 'pinia'
import ComponentPanel from '../ComponentPanel.vue'
import { componentLibrary } from '../../../config/component-library'

// Mock Ant Design Vue 组件
vi.mock('ant-design-vue', () => ({
  message: {
    success: vi.fn(),
    error: vi.fn(),
    warning: vi.fn()
  }
}))

describe('ComponentPanel', () => {
  beforeEach(() => {
    setActivePinia(createPinia())
  })

  it('应该正确渲染组件面板', () => {
    const wrapper = mount(ComponentPanel)
    
    // 检查搜索框是否存在
    expect(wrapper.find('input[placeholder="搜索组件..."]').exists()).toBe(true)
    
    // 检查分类是否正确渲染
    const categories = componentLibrary.categories
    expect(categories.length).toBeGreaterThan(0)
  })

  it('应该正确处理搜索功能', async () => {
    const wrapper = mount(ComponentPanel)
    
    // 输入搜索关键词
    const searchInput = wrapper.find('input[placeholder="搜索组件..."]')
    await searchInput.setValue('柱状图')
    
    // 触发搜索
    await wrapper.vm.handleSearch()
    
    // 验证搜索结果
    expect(wrapper.vm.filteredCategories).toBeDefined()
  })

  it('应该正确处理组件拖拽开始事件', () => {
    const wrapper = mount(ComponentPanel)
    
    // 模拟拖拽事件
    const mockComponent = {
      id: 'bar-chart',
      name: '柱状图',
      type: 'BAR_CHART'
    }
    
    const mockEvent = {
      dataTransfer: {
        setData: vi.fn(),
        effectAllowed: ''
      },
      target: {
        classList: {
          add: vi.fn()
        }
      }
    } as any
    
    wrapper.vm.handleDragStart(mockComponent, mockEvent)
    
    // 验证事件处理
    expect(mockEvent.dataTransfer.setData).toHaveBeenCalledWith(
      'application/json',
      JSON.stringify(mockComponent)
    )
    expect(mockEvent.dataTransfer.effectAllowed).toBe('copy')
  })

  it('应该正确处理组件拖拽结束事件', () => {
    const wrapper = mount(ComponentPanel)
    
    // 模拟DOM查询
    const mockElement = {
      classList: {
        remove: vi.fn()
      }
    }
    
    vi.spyOn(document, 'querySelectorAll').mockReturnValue([mockElement] as any)
    
    wrapper.vm.handleDragEnd()
    
    // 验证样式清理
    expect(mockElement.classList.remove).toHaveBeenCalledWith('dragging')
  })

  it('应该正确处理组件点击事件', () => {
    const wrapper = mount(ComponentPanel)
    
    const mockComponent = {
      id: 'bar-chart',
      name: '柱状图',
      type: 'BAR_CHART'
    }
    
    wrapper.vm.handleComponentClick(mockComponent)
    
    // 验证状态更新
    expect(wrapper.vm.currentComponent).toBe(mockComponent)
    expect(wrapper.vm.previewVisible).toBe(true)
  })

  it('应该正确发射事件', () => {
    const wrapper = mount(ComponentPanel)
    
    const mockComponent = {
      id: 'bar-chart',
      name: '柱状图',
      type: 'BAR_CHART'
    }
    
    const mockEvent = {
      dataTransfer: {
        setData: vi.fn(),
        effectAllowed: ''
      },
      target: {
        classList: {
          add: vi.fn()
        }
      }
    } as any
    
    // 测试拖拽开始事件
    wrapper.vm.handleDragStart(mockComponent, mockEvent)
    expect(wrapper.emitted('componentDragStart')).toBeTruthy()
    expect(wrapper.emitted('componentDragStart')?.[0]).toEqual([mockComponent, mockEvent])
    
    // 测试拖拽结束事件
    wrapper.vm.handleDragEnd()
    expect(wrapper.emitted('componentDragEnd')).toBeTruthy()
    
    // 测试组件选择事件
    wrapper.vm.handleComponentClick(mockComponent)
    expect(wrapper.emitted('componentSelect')).toBeTruthy()
    expect(wrapper.emitted('componentSelect')?.[0]).toEqual([mockComponent])
  })

  it('应该正确过滤组件分类', () => {
    const wrapper = mount(ComponentPanel)
    
    // 设置搜索关键词
    wrapper.vm.searchKeyword = '图表'
    
    const filtered = wrapper.vm.filteredCategories
    
    // 验证过滤结果
    expect(filtered).toBeDefined()
    expect(Array.isArray(filtered)).toBe(true)
    
    // 验证过滤逻辑
    filtered.forEach(category => {
      expect(category.components.length).toBeGreaterThanOrEqual(0)
    })
  })

  it('应该在组件挂载时正确初始化', () => {
    const wrapper = mount(ComponentPanel)
    
    // 验证初始状态
    expect(wrapper.vm.searchKeyword).toBe('')
    expect(wrapper.vm.activeCategories).toBeDefined()
    expect(wrapper.vm.previewVisible).toBe(false)
    expect(wrapper.vm.currentComponent).toBeNull()
    
    // 验证默认展开的分类
    if (componentLibrary.categories.length > 0) {
      expect(wrapper.vm.activeCategories).toContain(componentLibrary.categories[0].key)
    }
  })

  it('应该正确处理空搜索结果', () => {
    const wrapper = mount(ComponentPanel)
    
    // 设置不存在的搜索关键词
    wrapper.vm.searchKeyword = 'nonexistent'
    
    const filtered = wrapper.vm.filteredCategories
    
    // 验证空结果
    expect(filtered.length).toBe(0)
  })

  it('应该正确处理组件配置选项', () => {
    const wrapper = mount(ComponentPanel)
    
    // 选择一个有配置选项的组件
    const componentWithConfig = componentLibrary.categories[0]?.components[0]
    
    if (componentWithConfig) {
      wrapper.vm.currentComponent = componentWithConfig
      
      // 验证配置选项表格列
      expect(wrapper.vm.configColumns).toBeDefined()
      expect(wrapper.vm.configColumns.length).toBeGreaterThan(0)
      
      // 验证配置选项数据
      if (componentWithConfig.configOptions) {
        expect(componentWithConfig.configOptions.length).toBeGreaterThanOrEqual(0)
      }
    }
  })
})

/**
 * ReportDesigner 集成测试
 * @description 测试报表设计器的完整工作流程
 */

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { mount } from '@vue/test-utils'
import { createP<PERSON>, setActivePinia } from 'pinia'
import { nextTick } from 'vue'
import ReportDesigner from '../ReportDesigner.vue'
import { useReportStore } from '../../../stores/report'
import { useDataSourceStore } from '../../../stores/datasource'
import type { ReportConfig, ReportField } from '../../../types/report'
import type { DataSourceConfig } from '../../../types/datasource'

// Mock API 调用
vi.mock('../../../api/report', () => ({
  reportApi: {
    validateReport: vi.fn().mockResolvedValue({
      code: 200,
      data: { valid: true, errors: [] }
    }),
    createReport: vi.fn().mockResolvedValue({
      code: 200,
      data: { id: 'report-1', name: '测试报表' }
    }),
    updateReport: vi.fn().mockResolvedValue({
      code: 200,
      data: { id: 'report-1', name: '测试报表' }
    }),
    previewReport: vi.fn().mockResolvedValue({
      code: 200,
      data: { data: [{ name: '测试', value: 100 }] }
    }),
    publishReport: vi.fn().mockResolvedValue({
      code: 200,
      data: { success: true }
    })
  }
}))

vi.mock('../../../api/datasource', () => ({
  dataSourceApi: {
    getDataSources: vi.fn().mockResolvedValue({
      code: 200,
      data: {
        records: [
          {
            id: 'ds-1',
            name: '测试数据源',
            type: 'MYSQL',
            status: 'ACTIVE'
          }
        ],
        total: 1,
        current: 1
      }
    }),
    getMetadata: vi.fn().mockResolvedValue({
      code: 200,
      data: {
        tables: [
          {
            name: 'sales',
            comment: '销售表',
            columns: [
              {
                name: 'amount',
                dataType: 'decimal',
                comment: '金额',
                nullable: false,
                primaryKey: false
              },
              {
                name: 'category',
                dataType: 'varchar',
                comment: '分类',
                nullable: false,
                primaryKey: false
              }
            ]
          }
        ]
      }
    })
  }
}))

// Mock Ant Design Vue
vi.mock('ant-design-vue', () => ({
  message: {
    success: vi.fn(),
    error: vi.fn(),
    warning: vi.fn()
  }
}))

describe('ReportDesigner Integration Tests', () => {
  let wrapper: any
  let reportStore: any
  let dataSourceStore: any

  beforeEach(() => {
    setActivePinia(createPinia())
    reportStore = useReportStore()
    dataSourceStore = useDataSourceStore()
    
    wrapper = mount(ReportDesigner, {
      props: {
        reportId: undefined,
        templateId: undefined
      },
      global: {
        stubs: {
          'a-button': true,
          'a-input': true,
          'a-dropdown': true,
          'a-menu': true,
          'a-tabs': true,
          'a-tab-pane': true,
          'a-tooltip': true,
          'a-divider': true
        }
      }
    })
  })

  afterEach(() => {
    wrapper?.unmount()
  })

  describe('报表创建流程', () => {
    it('应该能够创建新报表', async () => {
      // 1. 设置报表基本信息
      wrapper.vm.reportConfig.name = '销售报表'
      wrapper.vm.reportConfig.type = 'TABLE'
      
      // 2. 选择数据源
      wrapper.vm.selectedDataSource = 'ds-1'
      await wrapper.vm.handleDataSourceChange?.('ds-1')
      
      // 3. 添加字段
      const field: ReportField = {
        id: 'field-1',
        name: 'amount',
        label: '销售金额',
        dataType: 'DECIMAL',
        fieldType: 'measure',
        required: false,
        visible: true,
        aggregation: 'SUM',
        sort: { enabled: false },
        filter: { enabled: false }
      }
      
      wrapper.vm.handleFieldSelect(field)
      
      // 4. 保存报表
      await wrapper.vm.saveReport()
      
      // 验证报表创建
      expect(wrapper.vm.reportConfig.id).toBeTruthy()
      expect(wrapper.vm.reportConfig.fields.length).toBe(1)
    })

    it('应该能够从模板创建报表', async () => {
      // 重新挂载组件，传入模板ID
      wrapper.unmount()
      wrapper = mount(ReportDesigner, {
        props: {
          templateId: 'template-1'
        }
      })
      
      // 模拟从模板创建
      vi.spyOn(reportStore, 'createFromTemplate').mockResolvedValue({
        id: 'report-from-template',
        name: '从模板创建的报表',
        type: 'CHART',
        fields: []
      })
      
      await wrapper.vm.$nextTick()
      
      // 验证模板创建
      expect(reportStore.createFromTemplate).toHaveBeenCalledWith('template-1', '新建报表')
    })
  })

  describe('数据源集成', () => {
    it('应该能够加载数据源列表', async () => {
      await dataSourceStore.fetchDataSources()
      
      expect(dataSourceStore.dataSources.length).toBe(1)
      expect(dataSourceStore.dataSources[0].name).toBe('测试数据源')
    })

    it('应该能够获取数据源元数据', async () => {
      const metadata = await dataSourceStore.getMetadata('ds-1')
      
      expect(metadata.tables.length).toBe(1)
      expect(metadata.tables[0].name).toBe('sales')
      expect(metadata.tables[0].columns.length).toBe(2)
    })

    it('应该能够处理数据源字段拖拽', async () => {
      // 模拟字段拖拽数据
      const fieldData = {
        type: 'field',
        data: {
          name: 'amount',
          title: '金额',
          type: 'decimal'
        }
      }
      
      const mockEvent = {
        preventDefault: vi.fn(),
        dataTransfer: {
          getData: vi.fn().mockReturnValue(JSON.stringify(fieldData))
        }
      }
      
      // 触发字段拖放
      wrapper.vm.handleCanvasDrop(mockEvent)
      
      expect(mockEvent.preventDefault).toHaveBeenCalled()
    })
  })

  describe('字段管理', () => {
    it('应该能够添加和管理字段', async () => {
      const initialFieldCount = wrapper.vm.reportConfig.fields.length
      
      // 添加维度字段
      const dimensionField: ReportField = {
        id: 'field-dimension',
        name: 'category',
        label: '分类',
        dataType: 'STRING',
        fieldType: 'dimension',
        required: false,
        visible: true,
        sort: { enabled: false },
        filter: { enabled: false }
      }
      
      wrapper.vm.handleFieldSelect(dimensionField)
      
      // 添加度量字段
      const measureField: ReportField = {
        id: 'field-measure',
        name: 'amount',
        label: '金额',
        dataType: 'DECIMAL',
        fieldType: 'measure',
        required: false,
        visible: true,
        aggregation: 'SUM',
        sort: { enabled: false },
        filter: { enabled: false }
      }
      
      wrapper.vm.handleFieldSelect(measureField)
      
      // 验证字段添加
      expect(wrapper.vm.reportConfig.fields.length).toBe(initialFieldCount + 2)
      
      // 验证字段分类
      const dimensionFields = wrapper.vm.reportConfig.fields.filter(f => f.fieldType === 'dimension')
      const measureFields = wrapper.vm.reportConfig.fields.filter(f => f.fieldType === 'measure')
      
      expect(dimensionFields.length).toBe(1)
      expect(measureFields.length).toBe(1)
    })

    it('应该能够移除字段', async () => {
      // 先添加字段
      const field: ReportField = {
        id: 'field-to-remove',
        name: 'test',
        label: '测试字段',
        dataType: 'STRING',
        fieldType: 'dimension',
        required: false,
        visible: true,
        sort: { enabled: false },
        filter: { enabled: false }
      }
      
      wrapper.vm.handleFieldSelect(field)
      const fieldCountAfterAdd = wrapper.vm.reportConfig.fields.length
      
      // 移除字段
      wrapper.vm.handleFieldRemove('field-to-remove')
      
      // 验证字段移除
      expect(wrapper.vm.reportConfig.fields.length).toBe(fieldCountAfterAdd - 1)
      expect(wrapper.vm.reportConfig.fields.find(f => f.id === 'field-to-remove')).toBeUndefined()
    })

    it('应该能够配置字段属性', async () => {
      const field: ReportField = {
        id: 'field-config',
        name: 'amount',
        label: '金额',
        dataType: 'DECIMAL',
        fieldType: 'measure',
        required: false,
        visible: true,
        aggregation: 'SUM',
        sort: { enabled: false },
        filter: { enabled: false }
      }
      
      wrapper.vm.handleFieldSelect(field)
      wrapper.vm.handleFieldConfig(field)
      
      // 验证配置弹窗状态
      expect(wrapper.vm.fieldConfigVisible).toBe(true)
      expect(wrapper.vm.configField).toBe(field)
    })
  })

  describe('报表操作', () => {
    it('应该能够预览报表', async () => {
      // 设置报表ID
      wrapper.vm.reportConfig.id = 'report-1'
      
      // 执行预览
      await wrapper.vm.previewReport()
      
      // 验证预览结果
      expect(wrapper.vm.previewData.length).toBe(1)
      expect(wrapper.vm.previewVisible).toBe(true)
    })

    it('应该能够发布报表', async () => {
      // 设置报表为草稿状态
      wrapper.vm.reportConfig.id = 'report-1'
      wrapper.vm.reportConfig.status = 'DRAFT'
      wrapper.vm.reportConfig.fields = [
        {
          id: 'field-1',
          name: 'amount',
          label: '金额',
          dataType: 'DECIMAL',
          fieldType: 'measure',
          required: false,
          visible: true,
          sort: { enabled: false },
          filter: { enabled: false }
        }
      ]
      
      // 执行发布
      await wrapper.vm.publishReport()
      
      // 验证发布状态
      expect(wrapper.vm.reportConfig.status).toBe('PUBLISHED')
    })

    it('应该能够处理撤销和重做', async () => {
      const originalName = wrapper.vm.reportConfig.name
      
      // 修改报表名称
      wrapper.vm.reportConfig.name = '修改后的名称'
      wrapper.vm.addToUndoStack()
      
      // 再次修改
      wrapper.vm.reportConfig.name = '再次修改的名称'
      wrapper.vm.addToUndoStack()
      
      // 撤销操作
      wrapper.vm.undoAction()
      expect(wrapper.vm.reportConfig.name).toBe('修改后的名称')
      
      // 重做操作
      wrapper.vm.redoAction()
      expect(wrapper.vm.reportConfig.name).toBe('再次修改的名称')
    })
  })

  describe('用户交互', () => {
    it('应该能够处理工具栏操作', async () => {
      // 测试网格切换
      const initialGridState = wrapper.vm.gridEnabled
      wrapper.vm.toggleGrid()
      expect(wrapper.vm.gridEnabled).toBe(!initialGridState)
      
      // 测试标尺切换
      const initialRulerState = wrapper.vm.rulerEnabled
      wrapper.vm.toggleRuler()
      expect(wrapper.vm.rulerEnabled).toBe(!initialRulerState)
      
      // 测试缩放
      wrapper.vm.handleZoomChange({ key: '150' })
      expect(wrapper.vm.zoomLevel).toBe(150)
    })

    it('应该能够处理画布交互', async () => {
      const mockEvent = {
        preventDefault: vi.fn()
      }
      
      // 测试画布拖拽
      wrapper.vm.handleCanvasDragOver(mockEvent)
      expect(mockEvent.preventDefault).toHaveBeenCalled()
      
      // 测试画布点击
      wrapper.vm.handleCanvasClick(mockEvent)
      expect(wrapper.vm.selectedElement).toBeNull()
      expect(wrapper.vm.selectedField).toBeNull()
    })
  })

  describe('错误处理', () => {
    it('应该能够处理保存失败', async () => {
      // 模拟验证失败
      vi.spyOn(reportStore, 'validateReport').mockResolvedValue({
        valid: false,
        errors: ['字段配置错误']
      })
      
      await wrapper.vm.saveReport()
      
      // 验证错误处理
      expect(wrapper.vm.saving).toBe(false)
    })

    it('应该能够处理预览失败', async () => {
      // 模拟预览失败
      vi.spyOn(reportStore, 'previewReport').mockRejectedValue(new Error('预览失败'))
      
      await wrapper.vm.previewReport()
      
      // 验证错误处理
      expect(wrapper.vm.previewing).toBe(false)
    })
  })

  describe('性能测试', () => {
    it('应该能够处理大量字段', async () => {
      const startTime = performance.now()
      
      // 添加大量字段
      const fields: ReportField[] = []
      for (let i = 0; i < 100; i++) {
        fields.push({
          id: `field-${i}`,
          name: `field${i}`,
          label: `字段${i}`,
          dataType: 'STRING',
          fieldType: 'dimension',
          required: false,
          visible: true,
          sort: { enabled: false },
          filter: { enabled: false }
        })
      }
      
      // 批量添加字段
      wrapper.vm.reportConfig.fields = fields
      
      await nextTick()
      
      const endTime = performance.now()
      const duration = endTime - startTime
      
      // 验证性能（应该在合理时间内完成）
      expect(duration).toBeLessThan(1000) // 1秒内
      expect(wrapper.vm.reportConfig.fields.length).toBe(100)
    })
  })
})

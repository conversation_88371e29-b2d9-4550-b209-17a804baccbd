<!--
  报表设计器组件
  @description 可视化报表设计界面，支持拖拽式设计和实时预览
-->
<template>
  <div class="report-designer">
    <!-- 工具栏 -->
    <div class="designer-toolbar">
      <div class="toolbar-left">
        <a-space>
          <a-button type="primary" @click="saveReport" :loading="saving">
            <SaveOutlined />
            保存
          </a-button>
          <a-button @click="previewReport" :loading="previewing">
            <EyeOutlined />
            预览
          </a-button>
          <a-button @click="publishReport" :disabled="!canPublish">
            <SendOutlined />
            发布
          </a-button>
          <a-divider type="vertical" />
          <a-button @click="undoAction" :disabled="!canUndo">
            <UndoOutlined />
            撤销
          </a-button>
          <a-button @click="redoAction" :disabled="!canRedo">
            <RedoOutlined />
            重做
          </a-button>
        </a-space>
      </div>
      <div class="toolbar-center">
        <a-input
          v-model:value="reportConfig.name"
          placeholder="请输入报表名称"
          style="width: 300px"
          @blur="updateReportName"
        />
      </div>
      <div class="toolbar-right">
        <a-space>
          <a-tooltip title="网格对齐">
            <a-button
              :type="gridEnabled ? 'primary' : 'default'"
              @click="toggleGrid"
            >
              <BorderOutlined />
            </a-button>
          </a-tooltip>
          <a-tooltip title="标尺">
            <a-button
              :type="rulerEnabled ? 'primary' : 'default'"
              @click="toggleRuler"
            >
              <ColumnWidthOutlined />
            </a-button>
          </a-tooltip>
          <a-dropdown>
            <template #overlay>
              <a-menu @click="handleZoomChange">
                <a-menu-item key="50">50%</a-menu-item>
                <a-menu-item key="75">75%</a-menu-item>
                <a-menu-item key="100">100%</a-menu-item>
                <a-menu-item key="125">125%</a-menu-item>
                <a-menu-item key="150">150%</a-menu-item>
                <a-menu-item key="200">200%</a-menu-item>
              </a-menu>
            </template>
            <a-button>
              {{ zoomLevel }}%
              <DownOutlined />
            </a-button>
          </a-dropdown>
        </a-space>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="designer-content">
      <!-- 左侧面板 -->
      <div class="left-panel">
        <a-tabs v-model:activeKey="leftActiveTab" size="small">
          <!-- 数据源 -->
          <a-tab-pane key="datasource" tab="数据源">
            <DataSourcePanel
              v-model:selected="selectedDataSource"
              @field-drag-start="handleFieldDragStart"
              @field-drag-end="handleFieldDragEnd"
            />
          </a-tab-pane>
          
          <!-- 字段 -->
          <a-tab-pane key="fields" tab="字段">
            <FieldPanel
              v-model:fields="reportConfig.fields"
              :available-fields="availableFields"
              @field-select="handleFieldSelect"
              @field-remove="handleFieldRemove"
              @field-configure="handleFieldConfig"
            />
          </a-tab-pane>
          
          <!-- 组件 -->
          <a-tab-pane key="components" tab="组件">
            <ComponentPanel
              @component-drag-start="handleComponentDragStart"
              @component-drag-end="handleComponentDragEnd"
              @component-select="handleComponentSelect"
            />
          </a-tab-pane>
        </a-tabs>
      </div>

      <!-- 中央设计区域 -->
      <div class="center-panel">
        <!-- 标尺 -->
        <div v-if="rulerEnabled" class="ruler-container">
          <div class="ruler-horizontal"></div>
          <div class="ruler-vertical"></div>
        </div>
        
        <!-- 设计画布 -->
        <div
          ref="designCanvas"
          class="design-canvas"
          :style="canvasStyle"
          @drop="handleCanvasDrop"
          @dragover="handleCanvasDragOver"
          @click="handleCanvasClick"
        >
          <!-- 网格背景 -->
          <div v-if="gridEnabled" class="grid-background"></div>
          
          <!-- 报表内容区域 -->
          <div class="report-content" :style="reportContentStyle">
            <!-- 页眉 -->
            <div v-if="reportConfig.layout?.header?.show" class="report-header">
              <ReportSection
                type="header"
                :config="reportConfig.layout.header"
                :fields="reportConfig.fields"
                @update="updateHeaderConfig"
              />
            </div>
            
            <!-- 报表主体 -->
            <div class="report-body">
              <!-- 表格报表 -->
              <div v-if="reportConfig.type === 'TABLE'" class="table-report">
                <div class="placeholder-content">
                  <TableOutlined style="font-size: 48px; color: #d9d9d9;" />
                  <p>表格设计器开发中...</p>
                </div>
              </div>

              <!-- 图表报表 -->
              <div v-else-if="reportConfig.type === 'CHART'" class="chart-report">
                <div class="placeholder-content">
                  <BarChartOutlined style="font-size: 48px; color: #d9d9d9;" />
                  <p>图表设计器开发中...</p>
                </div>
              </div>

              <!-- 交叉表报表 -->
              <div v-else-if="reportConfig.type === 'CROSSTAB'" class="crosstab-report">
                <div class="placeholder-content">
                  <BorderOutlined style="font-size: 48px; color: #d9d9d9;" />
                  <p>交叉表设计器开发中...</p>
                </div>
              </div>

              <!-- 混合报表 -->
              <div v-else-if="reportConfig.type === 'MIXED'" class="mixed-report">
                <div class="placeholder-content">
                  <DashboardOutlined style="font-size: 48px; color: #d9d9d9;" />
                  <p>混合报表设计器开发中...</p>
                </div>
              </div>
            </div>
            
            <!-- 页脚 */
            <div v-if="reportConfig.layout?.footer?.show" class="report-footer">
              <ReportSection
                type="footer"
                :config="reportConfig.layout.footer"
                :fields="reportConfig.fields"
                @update="updateFooterConfig"
              />
            </div>
          </div>
          
          <!-- 选择框 */
          <div
            v-if="selectedElement"
            class="selection-box"
            :style="selectionBoxStyle"
          >
            <div class="resize-handles">
              <div
                v-for="handle in resizeHandles"
                :key="handle"
                :class="['resize-handle', `resize-${handle}`]"
                @mousedown="startResize(handle, $event)"
              ></div>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧属性面板 -->
      <div class="right-panel">
        <a-tabs v-model:activeKey="rightActiveTab" size="small">
          <!-- 报表属性 -->
          <a-tab-pane key="report" tab="报表">
            <ReportPropertyPanel
              :config="reportConfig"
              @update="updateReportConfig"
            />
          </a-tab-pane>
          
          <!-- 字段属性 -->
          <a-tab-pane key="field" tab="字段" :disabled="!selectedField">
            <FieldPropertyPanel
              v-if="selectedField"
              :field="selectedField"
              @update="updateFieldConfig"
            />
          </a-tab-pane>
          
          <!-- 样式属性 */
          <a-tab-pane key="style" tab="样式">
            <StylePropertyPanel
              :config="reportConfig.style"
              @update="updateStyleConfig"
            />
          </a-tab-pane>
          
          <!-- 条件格式 -->
          <a-tab-pane key="conditional" tab="条件格式">
            <ConditionalFormattingPanel
              :fields="reportConfig.fields"
              @update="updateConditionalFormatting"
            />
          </a-tab-pane>
        </a-tabs>
      </div>
    </div>

    <!-- 预览弹窗 -->
    <ReportPreviewModal
      v-model:visible="previewVisible"
      :config="reportConfig"
      :data="previewData"
    />

    <!-- 字段配置弹窗 -->
    <FieldConfigModal
      v-model:visible="fieldConfigVisible"
      :field="configField"
      @save="saveFieldConfig"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, onUnmounted } from 'vue'
import { message } from 'ant-design-vue'
import {
  SaveOutlined,
  EyeOutlined,
  SendOutlined,
  UndoOutlined,
  RedoOutlined,
  BorderOutlined,
  ColumnWidthOutlined,
  DownOutlined,
  TableOutlined,
  BarChartOutlined,
  DashboardOutlined
} from '@ant-design/icons-vue'
import { useReportStore } from '../../stores/report'
import { useDataSourceStore } from '../../stores/datasource'
import DataSourcePanel from './DataSourcePanel.vue'
import FieldPanel from './FieldPanel.vue'
import ComponentPanel from './ComponentPanel.vue'
// import TableDesigner from './TableDesigner.vue'
// import ChartDesigner from './ChartDesigner.vue'
// import CrosstabDesigner from './CrosstabDesigner.vue'
// import MixedDesigner from './MixedDesigner.vue'
// import ReportSection from './ReportSection.vue'
// import ReportPropertyPanel from './ReportPropertyPanel.vue'
// import FieldPropertyPanel from './FieldPropertyPanel.vue'
// import StylePropertyPanel from './StylePropertyPanel.vue'
// import ConditionalFormattingPanel from './ConditionalFormattingPanel.vue'
// import ReportPreviewModal from './ReportPreviewModal.vue'
// import FieldConfigModal from './FieldConfigModal.vue'
import type { ReportConfig, ReportField } from '../../types/report'

// 组件属性
interface Props {
  reportId?: string
  templateId?: string
}

const props = defineProps<Props>()

// 组件事件
const emit = defineEmits<{
  save: [config: ReportConfig]
  preview: [config: ReportConfig]
  publish: [config: ReportConfig]
}>()

// 状态管理
const reportStore = useReportStore()
const dataSourceStore = useDataSourceStore()

// 响应式数据
const reportConfig = reactive<ReportConfig>({
  id: '',
  name: '新建报表',
  type: 'TABLE',
  dataSourceId: '',
  query: {},
  fields: [],
  layout: {
    pageSize: 'A4',
    orientation: 'portrait',
    margins: { top: 20, right: 20, bottom: 20, left: 20 }
  },
  style: {
    theme: 'default',
    font: { family: 'Arial', size: 12, color: '#000000' }
  },
  status: 'DRAFT',
  createdBy: '',
  createdAt: '',
  updatedAt: '',
  tenantId: ''
})

const selectedDataSource = ref('')
const selectedField = ref<ReportField | null>(null)
const selectedElement = ref<any>(null)
const configField = ref<ReportField | null>(null)
const availableFields = ref<any[]>([])
const previewData = ref<any[]>([])

const leftActiveTab = ref('datasource')
const rightActiveTab = ref('report')
const previewVisible = ref(false)
const fieldConfigVisible = ref(false)

const saving = ref(false)
const previewing = ref(false)
const gridEnabled = ref(true)
const rulerEnabled = ref(true)
const zoomLevel = ref(100)

const designCanvas = ref<HTMLElement>()
const undoStack = ref<any[]>([])
const redoStack = ref<any[]>([])

// 计算属性
const canPublish = computed(() => {
  return reportConfig.status === 'DRAFT' && reportConfig.fields.length > 0
})

const canUndo = computed(() => undoStack.value.length > 0)
const canRedo = computed(() => redoStack.value.length > 0)

const canvasStyle = computed(() => ({
  transform: `scale(${zoomLevel.value / 100})`,
  transformOrigin: 'top left'
}))

const reportContentStyle = computed(() => {
  const { pageSize, orientation, margins } = reportConfig.layout || {}
  const sizes = {
    A4: orientation === 'landscape' ? { width: 297, height: 210 } : { width: 210, height: 297 },
    A3: orientation === 'landscape' ? { width: 420, height: 297 } : { width: 297, height: 420 },
    A5: orientation === 'landscape' ? { width: 210, height: 148 } : { width: 148, height: 210 }
  }
  
  const size = sizes[pageSize || 'A4']
  return {
    width: `${size.width}mm`,
    height: `${size.height}mm`,
    padding: `${margins?.top || 20}mm ${margins?.right || 20}mm ${margins?.bottom || 20}mm ${margins?.left || 20}mm`
  }
})

const selectionBoxStyle = computed(() => {
  if (!selectedElement.value) return {}
  
  return {
    left: `${selectedElement.value.x}px`,
    top: `${selectedElement.value.y}px`,
    width: `${selectedElement.value.width}px`,
    height: `${selectedElement.value.height}px`
  }
})

const resizeHandles = ['nw', 'n', 'ne', 'e', 'se', 's', 'sw', 'w']

// 方法
const saveReport = async () => {
  try {
    saving.value = true
    
    // 验证报表配置
    const validation = await reportStore.validateReport(reportConfig)
    if (!validation.valid) {
      message.error(`报表配置有误: ${validation.errors?.join(', ')}`)
      return
    }
    
    // 保存报表
    if (reportConfig.id) {
      await reportStore.updateReport(reportConfig.id, reportConfig)
    } else {
      const newReport = await reportStore.createReport(reportConfig)
      reportConfig.id = newReport.id
    }
    
    // 添加到撤销栈
    addToUndoStack()
    
    emit('save', reportConfig)
  } catch (error) {
    console.error('保存报表失败:', error)
  } finally {
    saving.value = false
  }
}

const previewReport = async () => {
  try {
    previewing.value = true
    
    // 执行报表预览
    const result = await reportStore.previewReport(reportConfig.id, {}, 100)
    previewData.value = result.data
    previewVisible.value = true
    
    emit('preview', reportConfig)
  } catch (error) {
    console.error('预览报表失败:', error)
  } finally {
    previewing.value = false
  }
}

const publishReport = async () => {
  try {
    await reportStore.publishReport(reportConfig.id)
    reportConfig.status = 'PUBLISHED'
    emit('publish', reportConfig)
  } catch (error) {
    console.error('发布报表失败:', error)
  }
}

const updateReportName = () => {
  addToUndoStack()
}

const toggleGrid = () => {
  gridEnabled.value = !gridEnabled.value
}

const toggleRuler = () => {
  rulerEnabled.value = !rulerEnabled.value
}

const handleZoomChange = ({ key }: { key: string }) => {
  zoomLevel.value = parseInt(key)
}

const undoAction = () => {
  if (undoStack.value.length > 0) {
    const prevState = undoStack.value.pop()
    redoStack.value.push(JSON.parse(JSON.stringify(reportConfig)))
    Object.assign(reportConfig, prevState)
  }
}

const redoAction = () => {
  if (redoStack.value.length > 0) {
    const nextState = redoStack.value.pop()
    undoStack.value.push(JSON.parse(JSON.stringify(reportConfig)))
    Object.assign(reportConfig, nextState)
  }
}

const addToUndoStack = () => {
  undoStack.value.push(JSON.parse(JSON.stringify(reportConfig)))
  redoStack.value = [] // 清空重做栈
  
  // 限制撤销栈大小
  if (undoStack.value.length > 50) {
    undoStack.value.shift()
  }
}

// 拖拽处理
const handleFieldDragStart = (field: any) => {
  // 字段拖拽开始
}

const handleFieldDragEnd = () => {
  // 字段拖拽结束
}

const handleComponentDragStart = (component: any) => {
  // 组件拖拽开始
}

const handleComponentDragEnd = () => {
  // 组件拖拽结束
}

const handleComponentSelect = (component: any) => {
  // 组件选择处理
  console.log('选择组件:', component)
}

const handleCanvasDrop = (event: DragEvent) => {
  event.preventDefault()
  // 处理画布拖放
}

const handleCanvasDragOver = (event: DragEvent) => {
  event.preventDefault()
}

const handleCanvasClick = (event: MouseEvent) => {
  // 处理画布点击
  selectedElement.value = null
  selectedField.value = null
}

// 字段处理
const handleFieldSelect = (field: ReportField) => {
  if (!reportConfig.fields.find(f => f.id === field.id)) {
    reportConfig.fields.push(field)
    addToUndoStack()
  }
}

const handleFieldRemove = (fieldId: string) => {
  const index = reportConfig.fields.findIndex(f => f.id === fieldId)
  if (index >= 0) {
    reportConfig.fields.splice(index, 1)
    addToUndoStack()
  }
}

const handleFieldConfig = (field: ReportField) => {
  configField.value = field
  fieldConfigVisible.value = true
}

const saveFieldConfig = (field: ReportField) => {
  const index = reportConfig.fields.findIndex(f => f.id === field.id)
  if (index >= 0) {
    reportConfig.fields[index] = field
    addToUndoStack()
  }
  fieldConfigVisible.value = false
}

// 配置更新处理
const updateReportConfig = (config: Partial<ReportConfig>) => {
  Object.assign(reportConfig, config)
  addToUndoStack()
}

const updateFieldConfig = (field: ReportField) => {
  const index = reportConfig.fields.findIndex(f => f.id === field.id)
  if (index >= 0) {
    reportConfig.fields[index] = field
    addToUndoStack()
  }
}

const updateStyleConfig = (style: any) => {
  reportConfig.style = { ...reportConfig.style, ...style }
  addToUndoStack()
}

const updateHeaderConfig = (config: any) => {
  if (reportConfig.layout) {
    reportConfig.layout.header = config
    addToUndoStack()
  }
}

const updateFooterConfig = (config: any) => {
  if (reportConfig.layout) {
    reportConfig.layout.footer = config
    addToUndoStack()
  }
}

const updateTableConfig = (config: any) => {
  Object.assign(reportConfig, config)
  addToUndoStack()
}

const updateChartConfig = (config: any) => {
  reportConfig.chartConfig = config
  addToUndoStack()
}

const updateCrosstabConfig = (config: any) => {
  Object.assign(reportConfig, config)
  addToUndoStack()
}

const updateMixedConfig = (config: any) => {
  Object.assign(reportConfig, config)
  addToUndoStack()
}

const updateConditionalFormatting = (formatting: any) => {
  // 更新条件格式化
  addToUndoStack()
}

// 调整大小处理
const startResize = (handle: string, event: MouseEvent) => {
  event.preventDefault()
  // 开始调整大小
}

// 生命周期
onMounted(async () => {
  // 初始化报表配置
  if (props.reportId) {
    await reportStore.fetchReport(props.reportId)
    if (reportStore.currentReport) {
      Object.assign(reportConfig, reportStore.currentReport)
    }
  } else if (props.templateId) {
    // 从模板创建
    const newReport = await reportStore.createFromTemplate(props.templateId, '新建报表')
    Object.assign(reportConfig, newReport)
  }
  
  // 加载数据源列表
  await dataSourceStore.fetchDataSources()
})

onUnmounted(() => {
  // 清理资源
})
</script>

<style scoped lang="less">
.report-designer {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f5f5f5;
  
  .designer-toolbar {
    height: 48px;
    background: #fff;
    border-bottom: 1px solid #e8e8e8;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 16px;
    
    .toolbar-left,
    .toolbar-right {
      flex: 0 0 auto;
    }
    
    .toolbar-center {
      flex: 1;
      display: flex;
      justify-content: center;
    }
  }
  
  .designer-content {
    flex: 1;
    display: flex;
    overflow: hidden;
    
    .left-panel {
      width: 280px;
      background: #fff;
      border-right: 1px solid #e8e8e8;
      overflow: hidden;
    }
    
    .center-panel {
      flex: 1;
      position: relative;
      overflow: auto;
      background: #f0f0f0;
      
      .ruler-container {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        pointer-events: none;
        z-index: 10;
        
        .ruler-horizontal {
          height: 20px;
          background: #fff;
          border-bottom: 1px solid #ddd;
        }
        
        .ruler-vertical {
          width: 20px;
          background: #fff;
          border-right: 1px solid #ddd;
          position: absolute;
          top: 20px;
          bottom: 0;
        }
      }
      
      .design-canvas {
        min-height: 100%;
        position: relative;
        transform-origin: top left;
        
        .grid-background {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background-image: 
            linear-gradient(to right, #e0e0e0 1px, transparent 1px),
            linear-gradient(to bottom, #e0e0e0 1px, transparent 1px);
          background-size: 20px 20px;
          pointer-events: none;
        }
        
        .report-content {
          background: #fff;
          margin: 40px;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
          position: relative;
          
          .report-header,
          .report-footer {
            border-bottom: 1px dashed #ddd;
            min-height: 40px;
          }
          
          .report-body {
            min-height: 200px;

            .placeholder-content {
              height: 200px;
              display: flex;
              flex-direction: column;
              align-items: center;
              justify-content: center;
              color: #8c8c8c;

              p {
                margin-top: 8px;
                margin-bottom: 0;
                font-size: 14px;
              }
            }
          }
        }
        
        .selection-box {
          position: absolute;
          border: 2px solid #1890ff;
          pointer-events: none;
          z-index: 100;
          
          .resize-handles {
            .resize-handle {
              position: absolute;
              width: 8px;
              height: 8px;
              background: #1890ff;
              border: 1px solid #fff;
              pointer-events: auto;
              cursor: pointer;
              
              &.resize-nw { top: -4px; left: -4px; cursor: nw-resize; }
              &.resize-n { top: -4px; left: 50%; margin-left: -4px; cursor: n-resize; }
              &.resize-ne { top: -4px; right: -4px; cursor: ne-resize; }
              &.resize-e { top: 50%; right: -4px; margin-top: -4px; cursor: e-resize; }
              &.resize-se { bottom: -4px; right: -4px; cursor: se-resize; }
              &.resize-s { bottom: -4px; left: 50%; margin-left: -4px; cursor: s-resize; }
              &.resize-sw { bottom: -4px; left: -4px; cursor: sw-resize; }
              &.resize-w { top: 50%; left: -4px; margin-top: -4px; cursor: w-resize; }
            }
          }
        }
      }
    }
    
    .right-panel {
      width: 320px;
      background: #fff;
      border-left: 1px solid #e8e8e8;
      overflow: hidden;
    }
  }
}
</style>

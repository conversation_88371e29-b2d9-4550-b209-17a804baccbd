/**
 * 字典管理相关类型定义
 */

// 字典状态枚举
export enum DictStatus {
  DISABLED = 0, // 禁用
  ENABLED = 1   // 启用
}

// 字典类型接口
export interface DictType {
  id: number
  dictName: string      // 字典名称
  dictType: string      // 字典类型
  status: DictStatus    // 状态
  remark?: string       // 备注
  createBy?: string     // 创建者
  createTime?: string   // 创建时间
  updateBy?: string     // 更新者
  updateTime?: string   // 更新时间
}

// 字典数据接口
export interface DictData {
  id: number
  dictSort: number      // 字典排序
  dictLabel: string     // 字典标签
  dictValue: string     // 字典键值
  dictType: string      // 字典类型
  cssClass?: string     // 样式属性
  listClass?: string    // 表格回显样式
  isDefault: boolean    // 是否默认
  status: DictStatus    // 状态
  remark?: string       // 备注
  createBy?: string     // 创建者
  createTime?: string   // 创建时间
  updateBy?: string     // 更新者
  updateTime?: string   // 更新时间
}

// 字典类型查询参数
export interface DictTypeQueryParams {
  dictName?: string     // 字典名称
  dictType?: string     // 字典类型
  status?: DictStatus   // 状态
  pageNum?: number      // 页码
  pageSize?: number     // 每页数量
}

// 字典数据查询参数
export interface DictDataQueryParams {
  dictType: string      // 字典类型（必填）
  dictLabel?: string    // 字典标签
  status?: DictStatus   // 状态
  pageNum?: number      // 页码
  pageSize?: number     // 每页数量
}

// 字典类型创建请求
export interface DictTypeCreateRequest {
  dictName: string      // 字典名称
  dictType: string      // 字典类型
  status: DictStatus    // 状态
  remark?: string       // 备注
}

// 字典类型更新请求
export interface DictTypeUpdateRequest {
  dictName: string      // 字典名称
  dictType: string      // 字典类型
  status: DictStatus    // 状态
  remark?: string       // 备注
}

// 字典数据创建请求
export interface DictDataCreateRequest {
  dictSort: number      // 字典排序
  dictLabel: string     // 字典标签
  dictValue: string     // 字典键值
  dictType: string      // 字典类型
  cssClass?: string     // 样式属性
  listClass?: string    // 表格回显样式
  isDefault: boolean    // 是否默认
  status: DictStatus    // 状态
  remark?: string       // 备注
}

// 字典数据更新请求
export interface DictDataUpdateRequest {
  dictSort: number      // 字典排序
  dictLabel: string     // 字典标签
  dictValue: string     // 字典键值
  dictType: string      // 字典类型
  cssClass?: string     // 样式属性
  listClass?: string    // 表格回显样式
  isDefault: boolean    // 是否默认
  status: DictStatus    // 状态
  remark?: string       // 备注
}

// API响应接口
export interface DictTypeListResponse {
  code: number
  message: string
  data: {
    records: DictType[]
    total: number
    current: number
    size: number
  }
}

export interface DictDataListResponse {
  code: number
  message: string
  data: {
    records: DictData[]
    total: number
    current: number
    size: number
  }
}

export interface DictResponse {
  code: number
  message: string
  data: DictType | DictData
}

// 字典状态选项
export const DICT_STATUS_OPTIONS = [
  { label: '启用', value: DictStatus.ENABLED, color: 'success' },
  { label: '禁用', value: DictStatus.DISABLED, color: 'default' }
]

// 表格回显样式选项
export const LIST_CLASS_OPTIONS = [
  { label: '默认', value: 'default' },
  { label: '主要', value: 'primary' },
  { label: '成功', value: 'success' },
  { label: '信息', value: 'info' },
  { label: '警告', value: 'warning' },
  { label: '危险', value: 'danger' }
]

// 工具函数
export const getDictStatusDesc = (status: DictStatus): string => {
  const option = DICT_STATUS_OPTIONS.find(item => item.value === status)
  return option?.label || '未知'
}

export const getDictStatusColor = (status: DictStatus): string => {
  const option = DICT_STATUS_OPTIONS.find(item => item.value === status)
  return option?.color || 'default'
}

// 验证函数
export const validateDictType = (dictType: string): boolean => {
  return /^[a-zA-Z][a-zA-Z0-9_]*$/.test(dictType)
}

export const validateDictValue = (dictValue: string): boolean => {
  return dictValue.trim().length > 0
}

// 格式化时间
export const formatDateTime = (dateTime?: string): string => {
  if (!dateTime) return '-'
  return new Date(dateTime).toLocaleString('zh-CN')
}

// 常用字典类型
export const COMMON_DICT_TYPES = [
  'sys_user_sex',      // 用户性别
  'sys_show_hide',     // 显示状态
  'sys_normal_disable', // 系统状态
  'sys_job_status',    // 任务状态
  'sys_yes_no',        // 是否
  'sys_notice_type',   // 通知类型
  'sys_notice_status', // 通知状态
  'sys_oper_type',     // 操作类型
  'sys_common_status'  // 通用状态
]

// 字典数据默认值
export const DEFAULT_DICT_DATA: Partial<DictData> = {
  dictSort: 0,
  isDefault: false,
  status: DictStatus.ENABLED,
  cssClass: '',
  listClass: 'default'
}

// 字典类型默认值
export const DEFAULT_DICT_TYPE: Partial<DictType> = {
  status: DictStatus.ENABLED
}

// 分页默认配置
export const DEFAULT_PAGE_CONFIG = {
  pageNum: 1,
  pageSize: 10,
  total: 0
}

// 表格列宽配置
export const DICT_TABLE_COLUMNS = {
  dictType: {
    id: 80,
    dictName: 150,
    dictType: 120,
    status: 80,
    remark: 200,
    createTime: 150,
    action: 200
  },
  dictData: {
    id: 80,
    dictSort: 80,
    dictLabel: 120,
    dictValue: 120,
    listClass: 100,
    isDefault: 80,
    status: 80,
    remark: 150,
    createTime: 150,
    action: 200
  }
}

// 搜索表单默认值
export const DEFAULT_DICT_TYPE_SEARCH: DictTypeQueryParams = {
  dictName: '',
  dictType: '',
  status: undefined,
  pageNum: 1,
  pageSize: 10
}

export const DEFAULT_DICT_DATA_SEARCH: Omit<DictDataQueryParams, 'dictType'> = {
  dictLabel: '',
  status: undefined,
  pageNum: 1,
  pageSize: 10
}

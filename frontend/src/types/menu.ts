/**
 * 菜单管理相关类型定义
 */

import type { BaseEntity, ApiResponse, PageResponse } from './common'

/**
 * 菜单类型枚举
 */
export enum MenuType {
  /** 目录 */
  DIRECTORY = 'M',
  /** 菜单 */
  MENU = 'C',
  /** 按钮 */
  BUTTON = 'F'
}

/**
 * 菜单状态枚举
 */
export enum MenuStatus {
  /** 禁用 */
  DISABLED = 0,
  /** 启用 */
  ENABLED = 1
}

/**
 * 菜单实体接口
 */
export interface Menu extends BaseEntity {
  /** 菜单名称 */
  title: string
  /** 菜单类型 */
  type: MenuType
  /** 父菜单ID */
  parentId: number
  /** 路由路径 */
  path?: string
  /** 组件路径 */
  component?: string
  /** 菜单图标 */
  icon?: string
  /** 权限标识 */
  permission?: string
  /** 显示排序 */
  sort: number
  /** 菜单状态 */
  status: MenuStatus
  /** 是否外链 */
  isFrame: boolean
  /** 是否缓存 */
  isCache: boolean
  /** 路由参数 */
  query?: string
  /** 是否可见 */
  visible: boolean
  /** 子菜单列表 */
  children?: Menu[]
  /** 父菜单信息 */
  parent?: Menu
  /** 菜单层级 */
  level?: number
  /** 菜单路径（面包屑用） */
  breadcrumb?: string[]
  /** 是否为叶子节点 */
  isLeaf?: boolean
  /** 菜单描述 */
  description?: string
}

/**
 * 菜单查询参数
 */
export interface MenuQueryParams {
  /** 菜单名称 */
  title?: string
  /** 菜单类型 */
  type?: MenuType
  /** 菜单状态 */
  status?: MenuStatus
  /** 父菜单ID */
  parentId?: number
  /** 是否可见 */
  visible?: boolean
  /** 页码 */
  page?: number
  /** 页面大小 */
  size?: number
}

/**
 * 菜单创建请求
 */
export interface MenuCreateRequest {
  /** 菜单名称 */
  title: string
  /** 菜单类型 */
  type: MenuType
  /** 父菜单ID */
  parentId?: number
  /** 路由路径 */
  path?: string
  /** 组件路径 */
  component?: string
  /** 菜单图标 */
  icon?: string
  /** 权限标识 */
  permission?: string
  /** 显示排序 */
  sort?: number
  /** 菜单状态 */
  status?: MenuStatus
  /** 是否外链 */
  isFrame?: boolean
  /** 是否缓存 */
  isCache?: boolean
  /** 路由参数 */
  query?: string
  /** 是否可见 */
  visible?: boolean
  /** 菜单描述 */
  description?: string
  /** 备注 */
  remark?: string
}

/**
 * 菜单更新请求
 */
export interface MenuUpdateRequest extends MenuCreateRequest {
  /** 菜单ID */
  id: number
  /** 版本号 */
  version?: number
}

/**
 * 菜单树节点
 */
export interface MenuTreeNode {
  /** 节点ID */
  id: number
  /** 节点标题 */
  title: string
  /** 节点值 */
  value: number
  /** 节点标签 */
  label: string
  /** 子节点 */
  children?: MenuTreeNode[]
  /** 是否禁用 */
  disabled?: boolean
  /** 是否可选 */
  selectable?: boolean
  /** 菜单类型 */
  type?: MenuType
  /** 菜单图标 */
  icon?: string
  /** 路由路径 */
  path?: string
}

/**
 * 菜单响应类型
 */
export type MenuListResponse = PageResponse<Menu>
export type MenuDetailResponse = ApiResponse<Menu>
export type MenuOperationResponse = ApiResponse<Menu>
export type MenuTreeResponse = ApiResponse<Menu[]>

/**
 * 菜单类型选项
 */
export const MENU_TYPE_OPTIONS = [
  { label: '目录', value: MenuType.DIRECTORY, color: 'blue' },
  { label: '菜单', value: MenuType.MENU, color: 'green' },
  { label: '按钮', value: MenuType.BUTTON, color: 'orange' }
]

/**
 * 菜单状态选项
 */
export const MENU_STATUS_OPTIONS = [
  { label: '启用', value: MenuStatus.ENABLED, color: 'success' },
  { label: '禁用', value: MenuStatus.DISABLED, color: 'error' }
]

/**
 * 获取菜单类型描述
 */
export function getMenuTypeDesc(type: MenuType): string {
  const option = MENU_TYPE_OPTIONS.find(item => item.value === type)
  return option?.label || '未知'
}

/**
 * 获取菜单类型颜色
 */
export function getMenuTypeColor(type: MenuType): string {
  const option = MENU_TYPE_OPTIONS.find(item => item.value === type)
  return option?.color || 'default'
}

/**
 * 获取菜单状态描述
 */
export function getMenuStatusDesc(status: MenuStatus): string {
  const option = MENU_STATUS_OPTIONS.find(item => item.value === status)
  return option?.label || '未知'
}

/**
 * 获取菜单状态颜色
 */
export function getMenuStatusColor(status: MenuStatus): string {
  const option = MENU_STATUS_OPTIONS.find(item => item.value === status)
  return option?.color || 'default'
}

/**
 * 菜单转换为树节点
 */
export function convertToMenuTreeNode(menus: Menu[]): MenuTreeNode[] {
  return menus.map(menu => ({
    id: menu.id!,
    title: menu.title,
    value: menu.id!,
    label: menu.title,
    type: menu.type,
    icon: menu.icon,
    path: menu.path,
    children: menu.children ? convertToMenuTreeNode(menu.children) : undefined,
    disabled: menu.status === MenuStatus.DISABLED,
    selectable: true
  }))
}

/**
 * 构建菜单面包屑路径
 */
export function buildMenuBreadcrumb(menu: Menu, allMenus: Menu[]): string[] {
  const breadcrumb: string[] = []
  let currentMenu: Menu | undefined = menu
  
  while (currentMenu) {
    breadcrumb.unshift(currentMenu.title)
    if (currentMenu.parentId && currentMenu.parentId !== 0) {
      currentMenu = allMenus.find(m => m.id === currentMenu!.parentId)
    } else {
      currentMenu = undefined
    }
  }
  
  return breadcrumb
}

/**
 * 菜单路径验证规则
 */
export function validateMenuPath(path: string): boolean {
  // 路径必须以 / 开头
  if (!path.startsWith('/')) {
    return false
  }
  
  // 路径不能包含特殊字符（除了 / - _ .）
  const pathRegex = /^[a-zA-Z0-9/_.-]+$/
  return pathRegex.test(path)
}

/**
 * 组件路径验证规则
 */
export function validateComponentPath(component: string): boolean {
  // 组件路径可以为空
  if (!component) {
    return true
  }
  
  // 组件路径格式验证
  const componentRegex = /^[a-zA-Z0-9/_.-]+$/
  return componentRegex.test(component)
}

/**
 * 权限标识验证规则
 */
export function validatePermission(permission: string): boolean {
  // 权限标识可以为空
  if (!permission) {
    return true
  }
  
  // 权限标识格式：resource:action
  const permissionRegex = /^[a-zA-Z0-9_-]+:[a-zA-Z0-9_-]+$/
  return permissionRegex.test(permission)
}

/**
 * 常用菜单图标
 */
export const COMMON_MENU_ICONS = [
  'dashboard', 'user', 'setting', 'table', 'form', 'chart',
  'file', 'folder', 'home', 'shop', 'order', 'product',
  'finance', 'report', 'monitor', 'log', 'message', 'help'
]

/**
 * 角色管理相关类型定义
 * 基于后端API文档定义
 */

import type { BaseEntity, PageResult, ApiResponse } from './common'

/**
 * 角色类型枚举
 */
export enum RoleType {
  /** 系统管理员 */
  SYSTEM_ADMIN = 1,
  /** 租户管理员 */
  TENANT_ADMIN = 2,
  /** 普通角色 */
  NORMAL = 3
}

/**
 * 数据权限范围枚举
 */
export enum DataScope {
  /** 全部数据权限 */
  ALL = 1,
  /** 自定义数据权限 */
  CUSTOM = 2,
  /** 部门数据权限 */
  DEPT = 3,
  /** 部门及以下数据权限 */
  DEPT_AND_CHILD = 4,
  /** 仅本人数据权限 */
  SELF = 5
}

/**
 * 角色实体接口
 */
export interface Role extends BaseEntity {
  /** 角色编码 */
  roleCode: string
  /** 角色名称 */
  roleName: string
  /** 角色类型 */
  roleType: RoleType
  /** 角色描述 */
  description?: string
  /** 权限字符串 */
  permissions?: string
  /** 是否系统内置 */
  isSystem: boolean
  /** 是否默认角色 */
  isDefault: boolean
  /** 数据权限范围 */
  dataScope: DataScope
  /** 数据权限部门ID列表 */
  dataScopeDeptIds?: string
  /** 用户角色关联 */
  userRoles?: UserRole[]
  /** 角色权限关联 */
  rolePermissions?: RolePermission[]
  /** 是否系统管理员 */
  systemAdmin: boolean
  /** 是否租户管理员 */
  tenantAdmin: boolean
  /** 是否普通角色 */
  normalRole: boolean
  /** 角色类型描述 */
  roleTypeDesc: string
  /** 数据权限描述 */
  dataScopeDesc: string
}

/**
 * 用户角色关联
 */
export interface UserRole extends BaseEntity {
  /** 用户ID */
  userId: number
  /** 角色ID */
  roleId: number
  /** 授权人ID */
  grantedBy: number
  /** 授权时间 */
  grantedTime: string
  /** 过期时间 */
  expireTime?: string
  /** 用户信息 */
  user?: any
  /** 角色信息 */
  role?: Role
  /** 授权人信息 */
  grantedByUser?: any
  /** 是否已过期 */
  expired: boolean
  /** 是否有效 */
  valid: boolean
  /** 剩余天数 */
  remainingDays: number
  /** 状态描述 */
  statusDesc: string
}

/**
 * 角色权限关联
 */
export interface RolePermission extends BaseEntity {
  /** 角色ID */
  roleId: number
  /** 权限ID */
  permissionId: number
  /** 授权人ID */
  grantedBy: number
  /** 授权时间 */
  grantedTime: string
  /** 角色信息 */
  role?: Role
  /** 权限信息 */
  permission?: any
  /** 授权人信息 */
  grantedByUser?: any
  /** 是否有效 */
  valid: boolean
  /** 状态描述 */
  statusDesc: string
}

/**
 * 角色创建请求
 */
export interface RoleCreateRequest {
  /** 角色编码 */
  roleCode: string
  /** 角色名称 */
  roleName: string
  /** 角色类型 */
  roleType: RoleType
  /** 角色描述 */
  description?: string
  /** 权限编码列表 */
  permissionCodes?: string[]
  /** 权限ID列表 */
  permissionIds?: number[]
  /** 是否默认角色 */
  isDefault?: boolean
  /** 数据权限范围 */
  dataScope?: DataScope
  /** 数据权限部门ID列表 */
  dataScopeDeptIds?: number[]
  /** 排序 */
  sortOrder?: number
  /** 备注 */
  remark?: string
  /** 角色类型描述 */
  roleTypeDesc?: string
  /** 数据权限描述 */
  dataScopeDesc?: string
  /** 是否系统管理员 */
  systemAdmin?: boolean
  /** 是否租户管理员 */
  tenantAdmin?: boolean
  /** 是否普通角色 */
  normalRole?: boolean
}

/**
 * 角色更新请求
 */
export interface RoleUpdateRequest extends RoleCreateRequest {
  /** 角色ID */
  id: number
  /** 状态 */
  status?: number
  /** 版本号 */
  version?: number
  /** 是否启用 */
  enabled?: boolean
  /** 是否禁用 */
  disabled?: boolean
}

/**
 * 角色权限分配请求
 */
export interface RolePermissionRequest {
  /** 角色ID */
  roleId: number
  /** 权限ID列表 */
  permissionIds: number[]
  /** 权限编码列表 */
  permissionCodes?: string[]
  /** 操作类型：grant-授权，revoke-撤销 */
  operation: 'grant' | 'revoke'
  /** 备注 */
  remark?: string
  /** 是否授权操作 */
  grantOperation?: boolean
  /** 是否撤销操作 */
  revokeOperation?: boolean
  /** 操作描述 */
  operationDesc?: string
}

/**
 * 角色查询参数
 */
export interface RoleQueryParams {
  /** 搜索关键词 */
  keyword?: string
  /** 角色类型 */
  roleType?: RoleType
  /** 状态 */
  status?: number
  /** 页码 */
  page?: number
  /** 每页大小 */
  size?: number
}

/**
 * 角色列表响应
 */
export type RoleListResponse = ApiResponse<PageResult<Role>>

/**
 * 角色详情响应
 */
export type RoleDetailResponse = ApiResponse<Role>

/**
 * 角色操作响应
 */
export type RoleOperationResponse = ApiResponse<Role>

/**
 * 角色类型选项
 */
export const ROLE_TYPE_OPTIONS = [
  { label: '系统管理员', value: RoleType.SYSTEM_ADMIN },
  { label: '租户管理员', value: RoleType.TENANT_ADMIN },
  { label: '普通角色', value: RoleType.NORMAL }
]

/**
 * 数据权限范围选项
 */
export const DATA_SCOPE_OPTIONS = [
  { label: '全部数据权限', value: DataScope.ALL },
  { label: '自定义数据权限', value: DataScope.CUSTOM },
  { label: '部门数据权限', value: DataScope.DEPT },
  { label: '部门及以下数据权限', value: DataScope.DEPT_AND_CHILD },
  { label: '仅本人数据权限', value: DataScope.SELF }
]

/**
 * 获取角色类型描述
 */
export function getRoleTypeDesc(roleType: RoleType): string {
  const option = ROLE_TYPE_OPTIONS.find(item => item.value === roleType)
  return option?.label || '未知'
}

/**
 * 获取数据权限范围描述
 */
export function getDataScopeDesc(dataScope: DataScope): string {
  const option = DATA_SCOPE_OPTIONS.find(item => item.value === dataScope)
  return option?.label || '未知'
}

/**
 * 角色状态枚举
 */
export enum RoleStatus {
  /** 禁用 */
  DISABLED = 0,
  /** 启用 */
  ENABLED = 1
}

/**
 * 角色状态选项
 */
export const ROLE_STATUS_OPTIONS = [
  { label: '启用', value: RoleStatus.ENABLED, color: 'success' },
  { label: '禁用', value: RoleStatus.DISABLED, color: 'error' }
]

/**
 * 获取角色状态描述
 */
export function getRoleStatusDesc(status: number): string {
  const option = ROLE_STATUS_OPTIONS.find(item => item.value === status)
  return option?.label || '未知'
}

/**
 * 获取角色状态颜色
 */
export function getRoleStatusColor(status: number): string {
  const option = ROLE_STATUS_OPTIONS.find(item => item.value === status)
  return option?.color || 'default'
}

/**
 * 产品管理相关类型定义
 */

// 产品状态枚举
export enum ProductStatus {
  DISABLED = 0, // 下架
  ENABLED = 1   // 上架
}

// 产品类型枚举
export enum ProductType {
  PHYSICAL = 'PHYSICAL',    // 实物商品
  VIRTUAL = 'VIRTUAL',      // 虚拟商品
  SERVICE = 'SERVICE'       // 服务商品
}

// 库存预警类型枚举
export enum StockAlertType {
  NONE = 'NONE',           // 无预警
  LOW_STOCK = 'LOW_STOCK', // 低库存预警
  OUT_STOCK = 'OUT_STOCK'  // 缺货预警
}

// 产品分类接口
export interface ProductCategory {
  id: number
  categoryName: string     // 分类名称
  categoryCode: string     // 分类编码
  parentId?: number        // 父分类ID
  level: number           // 分类层级
  sort: number            // 排序
  status: ProductStatus   // 状态
  remark?: string         // 备注
  children?: ProductCategory[] // 子分类
  createBy?: string       // 创建者
  createTime?: string     // 创建时间
  updateBy?: string       // 更新者
  updateTime?: string     // 更新时间
}

// 产品接口
export interface Product {
  id: number
  productName: string     // 产品名称
  productCode: string     // 产品编码
  categoryId: number      // 分类ID
  categoryName?: string   // 分类名称
  productType: ProductType // 产品类型
  brand?: string          // 品牌
  model?: string          // 型号
  specification?: string  // 规格
  unit: string           // 单位
  purchasePrice: number  // 采购价格
  salePrice: number      // 销售价格
  marketPrice?: number   // 市场价格
  costPrice?: number     // 成本价格
  weight?: number        // 重量(kg)
  volume?: number        // 体积(m³)
  barcode?: string       // 条形码
  qrcode?: string        // 二维码
  images?: string[]      // 产品图片
  description?: string   // 产品描述
  status: ProductStatus  // 状态
  stockQuantity: number  // 库存数量
  minStock: number       // 最小库存
  maxStock?: number      // 最大库存
  stockAlertType: StockAlertType // 库存预警类型
  remark?: string        // 备注
  createBy?: string      // 创建者
  createTime?: string    // 创建时间
  updateBy?: string      // 更新者
  updateTime?: string    // 更新时间
}

// 产品查询参数
export interface ProductQueryParams {
  productName?: string    // 产品名称
  productCode?: string    // 产品编码
  categoryId?: number     // 分类ID
  productType?: ProductType // 产品类型
  brand?: string          // 品牌
  status?: ProductStatus  // 状态
  stockAlertType?: StockAlertType // 库存预警类型
  minPrice?: number       // 最小价格
  maxPrice?: number       // 最大价格
  pageNum?: number        // 页码
  pageSize?: number       // 每页数量
}

// 产品分类查询参数
export interface ProductCategoryQueryParams {
  categoryName?: string   // 分类名称
  categoryCode?: string   // 分类编码
  parentId?: number       // 父分类ID
  status?: ProductStatus  // 状态
  pageNum?: number        // 页码
  pageSize?: number       // 每页数量
}

// 产品创建请求
export interface ProductCreateRequest {
  productName: string     // 产品名称
  productCode: string     // 产品编码
  categoryId: number      // 分类ID
  productType: ProductType // 产品类型
  brand?: string          // 品牌
  model?: string          // 型号
  specification?: string  // 规格
  unit: string           // 单位
  purchasePrice: number  // 采购价格
  salePrice: number      // 销售价格
  marketPrice?: number   // 市场价格
  costPrice?: number     // 成本价格
  weight?: number        // 重量
  volume?: number        // 体积
  barcode?: string       // 条形码
  qrcode?: string        // 二维码
  images?: string[]      // 产品图片
  description?: string   // 产品描述
  status: ProductStatus  // 状态
  stockQuantity: number  // 库存数量
  minStock: number       // 最小库存
  maxStock?: number      // 最大库存
  stockAlertType: StockAlertType // 库存预警类型
  remark?: string        // 备注
}

// 产品更新请求
export interface ProductUpdateRequest {
  productName: string     // 产品名称
  productCode: string     // 产品编码
  categoryId: number      // 分类ID
  productType: ProductType // 产品类型
  brand?: string          // 品牌
  model?: string          // 型号
  specification?: string  // 规格
  unit: string           // 单位
  purchasePrice: number  // 采购价格
  salePrice: number      // 销售价格
  marketPrice?: number   // 市场价格
  costPrice?: number     // 成本价格
  weight?: number        // 重量
  volume?: number        // 体积
  barcode?: string       // 条形码
  qrcode?: string        // 二维码
  images?: string[]      // 产品图片
  description?: string   // 产品描述
  status: ProductStatus  // 状态
  stockQuantity: number  // 库存数量
  minStock: number       // 最小库存
  maxStock?: number      // 最大库存
  stockAlertType: StockAlertType // 库存预警类型
  remark?: string        // 备注
}

// 产品分类创建请求
export interface ProductCategoryCreateRequest {
  categoryName: string    // 分类名称
  categoryCode: string    // 分类编码
  parentId?: number       // 父分类ID
  sort: number           // 排序
  status: ProductStatus  // 状态
  remark?: string        // 备注
}

// 产品分类更新请求
export interface ProductCategoryUpdateRequest {
  categoryName: string    // 分类名称
  categoryCode: string    // 分类编码
  parentId?: number       // 父分类ID
  sort: number           // 排序
  status: ProductStatus  // 状态
  remark?: string        // 备注
}

// API响应接口
export interface ProductListResponse {
  code: number
  message: string
  data: {
    records: Product[]
    total: number
    current: number
    size: number
  }
}

export interface ProductCategoryListResponse {
  code: number
  message: string
  data: {
    records: ProductCategory[]
    total: number
    current: number
    size: number
  }
}

export interface ProductResponse {
  code: number
  message: string
  data: Product | ProductCategory
}

// 产品状态选项
export const PRODUCT_STATUS_OPTIONS = [
  { label: '上架', value: ProductStatus.ENABLED, color: 'success' },
  { label: '下架', value: ProductStatus.DISABLED, color: 'default' }
]

// 产品类型选项
export const PRODUCT_TYPE_OPTIONS = [
  { label: '实物商品', value: ProductType.PHYSICAL },
  { label: '虚拟商品', value: ProductType.VIRTUAL },
  { label: '服务商品', value: ProductType.SERVICE }
]

// 库存预警类型选项
export const STOCK_ALERT_TYPE_OPTIONS = [
  { label: '无预警', value: StockAlertType.NONE, color: 'default' },
  { label: '低库存预警', value: StockAlertType.LOW_STOCK, color: 'warning' },
  { label: '缺货预警', value: StockAlertType.OUT_STOCK, color: 'error' }
]

// 常用单位选项
export const UNIT_OPTIONS = [
  '个', '件', '台', '套', '箱', '包', '袋', '瓶', '罐', '桶',
  'kg', 'g', 't', 'L', 'ml', 'm', 'cm', 'mm', 'm²', 'm³'
]

// 工具函数
export const getProductStatusDesc = (status: ProductStatus): string => {
  const option = PRODUCT_STATUS_OPTIONS.find(item => item.value === status)
  return option?.label || '未知'
}

export const getProductStatusColor = (status: ProductStatus): string => {
  const option = PRODUCT_STATUS_OPTIONS.find(item => item.value === status)
  return option?.color || 'default'
}

export const getProductTypeDesc = (type: ProductType): string => {
  const option = PRODUCT_TYPE_OPTIONS.find(item => item.value === type)
  return option?.label || '未知'
}

export const getStockAlertTypeDesc = (type: StockAlertType): string => {
  const option = STOCK_ALERT_TYPE_OPTIONS.find(item => item.value === type)
  return option?.label || '未知'
}

export const getStockAlertTypeColor = (type: StockAlertType): string => {
  const option = STOCK_ALERT_TYPE_OPTIONS.find(item => item.value === type)
  return option?.color || 'default'
}

// 验证函数
export const validateProductCode = (code: string): boolean => {
  return /^[A-Z0-9][A-Z0-9_-]*$/.test(code)
}

export const validatePrice = (price: number): boolean => {
  return price >= 0 && price <= 999999.99
}

export const validateStock = (stock: number): boolean => {
  return Number.isInteger(stock) && stock >= 0
}

// 格式化价格
export const formatPrice = (price: number): string => {
  return `¥${price.toFixed(2)}`
}

// 格式化库存
export const formatStock = (stock: number, unit: string): string => {
  return `${stock} ${unit}`
}

// 默认值
export const DEFAULT_PRODUCT: Partial<Product> = {
  productType: ProductType.PHYSICAL,
  status: ProductStatus.ENABLED,
  stockAlertType: StockAlertType.LOW_STOCK,
  unit: '个',
  purchasePrice: 0,
  salePrice: 0,
  stockQuantity: 0,
  minStock: 10
}

export const DEFAULT_PRODUCT_CATEGORY: Partial<ProductCategory> = {
  status: ProductStatus.ENABLED,
  level: 1,
  sort: 0
}

// 分页默认配置
export const DEFAULT_PRODUCT_PAGE_CONFIG = {
  pageNum: 1,
  pageSize: 10,
  total: 0
}

/**
 * 认证相关类型定义
 */

// 登录请求参数
export interface LoginParams {
  username: string
  password: string
  rememberMe?: boolean
  deviceInfo?: string
  ipAddress?: string
}

// 登录响应数据
export interface LoginResult {
  accessToken: string
  tokenType: string
  expiresIn: number
  userInfo: UserInfo
}

// 用户信息
export interface UserInfo {
  id: number
  username: string
  email: string
  nickname?: string
  avatar?: string
  phone?: string
  userType: number
  accountStatus: number
  tenantId?: number
  tenantName?: string
  roles?: string[]
  permissions?: string[]
  lastLoginTime?: string
  createTime?: string
}

// 用户账户状态枚举
export enum AccountStatus {
  ACTIVE = 1,           // 激活
  LOCKED = 2,           // 锁定
  DISABLED = 3,         // 禁用
  PENDING_ACTIVATION = 4 // 待激活
}

// 用户类型枚举
export enum UserType {
  ADMIN = 1,      // 管理员
  TENANT_ADMIN = 2, // 租户管理员
  USER = 3        // 普通用户
}

// 租户信息
export interface TenantInfo {
  id: number
  name: string
  code: string
  status: number
  expireTime?: string
  contactName?: string
  contactPhone?: string
  contactEmail?: string
  domain?: string
  logo?: string
  description?: string
}

// JWT Token信息
export interface TokenInfo {
  accessToken: string
  refreshToken?: string
  tokenType: string
  expiresIn: number
  expiresAt: number // 过期时间戳
}

// 权限信息
export interface PermissionInfo {
  roles: string[]
  permissions: string[]
  menus: MenuInfo[]
}

// 菜单信息
export interface MenuInfo {
  id: number
  parentId: number
  name: string
  path: string
  component?: string
  icon?: string
  sort: number
  visible: boolean
  children?: MenuInfo[]
}

// 社交登录类型
export enum SocialType {
  WECHAT = 1,    // 微信
  QQ = 2,        // QQ
  ALIPAY = 3,    // 支付宝
  GITHUB = 4,    // GitHub
  GITEE = 5      // Gitee
}

// 社交登录参数
export interface SocialLoginParams {
  type: SocialType
  code: string
  state: string
  username?: string
  password?: string
}

// 社交绑定参数
export interface SocialBindParams {
  type: SocialType
  code: string
  state: string
}

// 社交登录回调结果
export interface SocialCallbackResult {
  type: SocialType
  code: string
  state: string
  nickname: string
  avatar: string
  bound: boolean
}

// 密码修改参数
export interface ChangePasswordParams {
  oldPassword: string
  newPassword: string
  confirmPassword: string
}

// 用户资料更新参数
export interface UpdateProfileParams {
  nickname?: string
  email?: string
  phone?: string
  avatar?: string
}

// 认证状态
export interface AuthState {
  // 是否已登录
  isLoggedIn: boolean
  // 用户信息
  userInfo: UserInfo | null
  // Token信息
  tokenInfo: TokenInfo | null
  // 权限信息
  permissions: PermissionInfo | null
  // 租户信息
  tenantInfo: TenantInfo | null
  // 登录加载状态
  loginLoading: boolean
  // 用户信息加载状态
  userInfoLoading: boolean
}

// API响应状态码
export enum ApiCode {
  SUCCESS = '200',
  UNAUTHORIZED = '401',
  FORBIDDEN = '403',
  NOT_FOUND = '404',
  INTERNAL_ERROR = '500',
  BAD_REQUEST = '400',
  TOKEN_EXPIRED = '401001',
  INVALID_TOKEN = '401002',
  PERMISSION_DENIED = '403001',
  TENANT_EXPIRED = '403002',
  ACCOUNT_LOCKED = '403003',
  ACCOUNT_DISABLED = '403004'
}

// 错误信息映射
export const ErrorMessages: Record<string, string> = {
  [ApiCode.UNAUTHORIZED]: '登录已过期，请重新登录',
  [ApiCode.FORBIDDEN]: '没有权限访问该资源',
  [ApiCode.NOT_FOUND]: '请求的资源不存在',
  [ApiCode.INTERNAL_ERROR]: '服务器内部错误',
  [ApiCode.BAD_REQUEST]: '请求参数错误',
  [ApiCode.TOKEN_EXPIRED]: 'Token已过期，请重新登录',
  [ApiCode.INVALID_TOKEN]: '无效的Token',
  [ApiCode.PERMISSION_DENIED]: '权限不足',
  [ApiCode.TENANT_EXPIRED]: '租户已过期',
  [ApiCode.ACCOUNT_LOCKED]: '账户已被锁定',
  [ApiCode.ACCOUNT_DISABLED]: '账户已被禁用'
}

// 本地存储键名
export enum StorageKeys {
  TOKEN = 'visthink_token',
  REFRESH_TOKEN = 'visthink_refresh_token',
  USER_INFO = 'visthink_user_info',
  TENANT_INFO = 'visthink_tenant_info',
  PERMISSIONS = 'visthink_permissions',
  REMEMBER_ME = 'visthink_remember_me',
  LAST_LOGIN_USERNAME = 'visthink_last_login_username'
}

// 认证事件类型
export enum AuthEventType {
  LOGIN_SUCCESS = 'login_success',
  LOGIN_FAILED = 'login_failed',
  LOGOUT = 'logout',
  TOKEN_EXPIRED = 'token_expired',
  PERMISSION_DENIED = 'permission_denied',
  USER_INFO_UPDATED = 'user_info_updated'
}

// 认证事件数据
export interface AuthEvent {
  type: AuthEventType
  data?: any
  timestamp: number
}

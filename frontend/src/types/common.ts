/**
 * 通用类型定义
 * 基于后端API响应结构
 */

// 基础实体类型
export interface BaseEntity {
  id?: number
  createBy?: string
  createTime?: string
  createName?: string
  updateName?: string
  updateBy?: string
  updateTime?: string
  tenantId?: number
  remark?: string
  deleted?: number
  version?: number
  status?: number
  sortOrder?: number
  extField1?: string
  extField2?: string
  extField3?: string
  extJson?: string
  new?: boolean
  enabled?: boolean
  disabled?: boolean
  tenant?: number
  entityDescription?: string
}

// API响应基础结构
export interface ApiResponse<T = any> {
  code: string
  message: string
  data: T
  timestamp: string
  requestId: string
  duration: number
  extra?: Record<string, any>
  success: boolean
  error: boolean
}

// 分页结果
export interface PageResult<T = any> {
  records: T[]
  total: number
  pageNum: number
  pageSize: number
  totalPages: number
  hasNext: boolean
  hasPrevious: boolean
  isFirst: boolean
  isLast: boolean
  size: number
  isEmpty: boolean
  duration: number
  extra?: Record<string, any>
  startRow: number
  endRow: number
  summary: string
  page: number
  pages: number
}

// 分页查询参数
export interface PageQuery {
  page?: number
  size?: number
  keyword?: string
  sortField?: string
  sortOrder?: 'asc' | 'desc'
}

// 树形结构节点
export interface TreeNode<T = any> {
  id: number
  parentId?: number
  children?: TreeNode<T>[]
  level?: number
  leaf?: boolean
  expanded?: boolean
  selected?: boolean
  data?: T
}

// 选项类型
export interface Option<T = any> {
  label: string
  value: T
  disabled?: boolean
  color?: string
  icon?: string
  description?: string
}

// 表格列配置
export interface TableColumn {
  key: string
  title: string
  dataIndex: string
  width?: number
  minWidth?: number
  maxWidth?: number
  fixed?: 'left' | 'right'
  align?: 'left' | 'center' | 'right'
  sorter?: boolean | ((a: any, b: any) => number)
  sortOrder?: 'ascend' | 'descend' | null
  filters?: Array<{ text: string; value: any }>
  filteredValue?: any[]
  filterMultiple?: boolean
  filterDropdown?: boolean
  ellipsis?: boolean
  resizable?: boolean
  customRender?: (value: any, record: any, index: number) => any
}

// 表单字段配置
export interface FormField {
  name: string
  label: string
  type: 'input' | 'textarea' | 'select' | 'radio' | 'checkbox' | 'date' | 'datetime' | 'number' | 'password' | 'upload'
  required?: boolean
  placeholder?: string
  options?: Option[]
  rules?: any[]
  props?: Record<string, any>
  span?: number
  offset?: number
  hidden?: boolean
  disabled?: boolean
  readonly?: boolean
}

// 搜索表单配置
export interface SearchForm {
  fields: FormField[]
  layout?: 'horizontal' | 'vertical' | 'inline'
  labelCol?: { span: number }
  wrapperCol?: { span: number }
  collapsed?: boolean
  showReset?: boolean
  showSearch?: boolean
}

// 操作按钮配置
export interface ActionButton {
  key: string
  label: string
  type?: 'primary' | 'default' | 'dashed' | 'text' | 'link'
  danger?: boolean
  icon?: string
  permission?: string
  disabled?: boolean
  loading?: boolean
  onClick?: (record?: any) => void
}

// 批量操作配置
export interface BatchAction {
  key: string
  label: string
  icon?: string
  permission?: string
  danger?: boolean
  confirm?: boolean
  confirmTitle?: string
  confirmContent?: string
  onClick?: (selectedKeys: any[], selectedRows: any[]) => void
}

// 导入导出配置
export interface ImportExportConfig {
  import?: {
    enabled: boolean
    templateUrl?: string
    accept?: string
    maxSize?: number
    columns: string[]
  }
  export?: {
    enabled: boolean
    formats: Array<'excel' | 'csv' | 'pdf'>
    columns: string[]
  }
}

// 权限配置
export interface PermissionConfig {
  module: string
  actions: {
    view?: string
    create?: string
    update?: string
    delete?: string
    export?: string
    import?: string
    [key: string]: string | undefined
  }
}

// 状态枚举
export enum Status {
  DISABLED = 0,
  ENABLED = 1
}

// 删除状态枚举
export enum DeleteStatus {
  NORMAL = 0,
  DELETED = 1
}

// 通用状态选项
export const STATUS_OPTIONS: Option<Status>[] = [
  { label: '启用', value: Status.ENABLED, color: 'green' },
  { label: '禁用', value: Status.DISABLED, color: 'red' }
]

// 是否选项
export const YES_NO_OPTIONS: Option<boolean>[] = [
  { label: '是', value: true },
  { label: '否', value: false }
]

// 排序选项
export const SORT_ORDER_OPTIONS: Option<string>[] = [
  { label: '升序', value: 'asc' },
  { label: '降序', value: 'desc' }
]

// 日期格式常量
export const DATE_FORMAT = 'YYYY-MM-DD'
export const DATETIME_FORMAT = 'YYYY-MM-DD HH:mm:ss'
export const TIME_FORMAT = 'HH:mm:ss'

// 分页默认配置
export const DEFAULT_PAGE_SIZE = 10
export const PAGE_SIZE_OPTIONS = ['10', '20', '50', '100']

// 表格默认配置
export const DEFAULT_TABLE_CONFIG = {
  size: 'middle' as const,
  bordered: true,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number, range: [number, number]) => 
    `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
  pageSizeOptions: PAGE_SIZE_OPTIONS,
  defaultPageSize: DEFAULT_PAGE_SIZE
}

// HTTP状态码
export enum HttpStatus {
  OK = 200,
  CREATED = 201,
  NO_CONTENT = 204,
  BAD_REQUEST = 400,
  UNAUTHORIZED = 401,
  FORBIDDEN = 403,
  NOT_FOUND = 404,
  INTERNAL_SERVER_ERROR = 500
}

// 请求方法
export enum HttpMethod {
  GET = 'GET',
  POST = 'POST',
  PUT = 'PUT',
  DELETE = 'DELETE',
  PATCH = 'PATCH'
}

// 文件上传配置
export interface UploadConfig {
  action: string
  accept?: string
  maxSize?: number
  maxCount?: number
  multiple?: boolean
  directory?: boolean
  headers?: Record<string, string>
  data?: Record<string, any>
  beforeUpload?: (file: File) => boolean | Promise<boolean>
  onChange?: (info: any) => void
}

// 通知配置
export interface NotificationConfig {
  type: 'success' | 'info' | 'warning' | 'error'
  title: string
  message?: string
  duration?: number
  placement?: 'topLeft' | 'topRight' | 'bottomLeft' | 'bottomRight'
}

// 确认对话框配置
export interface ConfirmConfig {
  title: string
  content?: string
  okText?: string
  cancelText?: string
  okType?: 'primary' | 'danger'
  icon?: string
  onOk?: () => void | Promise<void>
  onCancel?: () => void
}

// 加载状态
export interface LoadingState {
  loading: boolean
  error?: string | null
  data?: any
}

// 异步操作状态
export interface AsyncState<T = any> extends LoadingState {
  data?: T
  lastUpdated?: number
}

// 表单验证规则
export interface ValidationRule {
  required?: boolean
  message?: string
  type?: 'string' | 'number' | 'boolean' | 'method' | 'regexp' | 'integer' | 'float' | 'array' | 'object' | 'enum' | 'date' | 'url' | 'hex' | 'email'
  pattern?: RegExp
  min?: number
  max?: number
  len?: number
  validator?: (rule: any, value: any) => Promise<void>
  trigger?: 'blur' | 'change' | ['blur', 'change']
}

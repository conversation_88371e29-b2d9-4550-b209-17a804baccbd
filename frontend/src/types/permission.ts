/**
 * 权限管理相关类型定义
 * 基于后端API文档定义
 */

import type { BaseEntity, PageResult, ApiResponse } from './common'
import type { RolePermission } from './role'

/**
 * 权限类型枚举
 */
export enum PermissionType {
  /** API权限 */
  API = 1,
  /** 菜单权限 */
  MENU = 2,
  /** 按钮权限 */
  BUTTON = 3,
  /** 数据权限 */
  DATA = 4
}

/**
 * 权限实体接口
 */
export interface Permission extends BaseEntity {
  /** 权限编码 */
  permissionCode: string
  /** 权限名称 */
  permissionName: string
  /** 权限类型 */
  permissionType: PermissionType
  /** 父权限ID */
  parentId?: number
  /** 资源类型 */
  resourceType?: string
  /** 资源路径 */
  resourcePath?: string
  /** 操作动作 */
  action?: string
  /** 权限描述 */
  description?: string
  /** 是否系统内置 */
  isSystem: boolean
  /** 子权限列表 */
  children?: Permission[]
  /** 父权限信息 */
  parent?: Permission
  /** 角色权限关联 */
  rolePermissions?: RolePermission[]
  /** 是否API权限 */
  apiPermission: boolean
  /** 是否菜单权限 */
  menuPermission: boolean
  /** 是否按钮权限 */
  buttonPermission: boolean
  /** 是否数据权限 */
  dataPermission: boolean
  /** 是否根权限 */
  rootPermission: boolean
  /** 权限类型描述 */
  permissionTypeDesc: string
  /** 权限编码是否有效 */
  validPermissionCode: boolean
  /** 从编码解析的资源 */
  resourceFromCode: string
  /** 从编码解析的动作 */
  actionFromCode: string
}

/**
 * 权限创建请求
 */
export interface PermissionCreateRequest {
  /** 权限编码 */
  permissionCode: string
  /** 权限名称 */
  permissionName: string
  /** 权限类型 */
  permissionType: PermissionType
  /** 父权限ID */
  parentId?: number
  /** 资源类型 */
  resourceType?: string
  /** 资源路径 */
  resourcePath?: string
  /** 操作动作 */
  action?: string
  /** 权限描述 */
  description?: string
  /** 是否系统内置 */
  isSystem?: boolean
  /** 排序 */
  sortOrder?: number
  /** 备注 */
  remark?: string
  /** 权限编码是否有效 */
  validPermissionCode?: boolean
  /** 从编码解析的资源 */
  resourceFromCode?: string
  /** 从编码解析的动作 */
  actionFromCode?: string
  /** 权限类型描述 */
  permissionTypeDesc?: string
  /** 是否API权限 */
  apiPermission?: boolean
  /** 是否菜单权限 */
  menuPermission?: boolean
  /** 是否按钮权限 */
  buttonPermission?: boolean
  /** 是否数据权限 */
  dataPermission?: boolean
  /** 是否根权限 */
  rootPermission?: boolean
}

/**
 * 权限更新请求
 */
export interface PermissionUpdateRequest extends PermissionCreateRequest {
  /** 权限ID */
  id: number
  /** 状态 */
  status?: number
  /** 版本号 */
  version?: number
}

/**
 * 权限查询参数
 */
export interface PermissionQueryParams {
  /** 搜索关键词 */
  keyword?: string
  /** 权限类型 */
  permissionType?: PermissionType
  /** 状态 */
  status?: number
  /** 页码 */
  page?: number
  /** 每页大小 */
  size?: number
}

/**
 * 按钮权限配置
 */
export interface ButtonPermission {
  /** 按钮编码 */
  code: string
  /** 按钮名称 */
  name: string
  /** 权限编码 */
  permissionCode?: string
  /** 图标 */
  icon?: string
  /** 类型 */
  type?: string
  /** 是否启用 */
  enabled?: boolean
  /** 排序 */
  sortOrder?: number
}

/**
 * 权限列表响应
 */
export type PermissionListResponse = ApiResponse<PageResult<Permission>>

/**
 * 权限详情响应
 */
export type PermissionDetailResponse = ApiResponse<Permission>

/**
 * 权限操作响应
 */
export type PermissionOperationResponse = ApiResponse<Permission>

/**
 * 权限树响应
 */
export type PermissionTreeResponse = ApiResponse<Permission[]>

/**
 * 权限类型选项
 */
export const PERMISSION_TYPE_OPTIONS = [
  { label: 'API权限', value: PermissionType.API, color: 'blue' },
  { label: '菜单权限', value: PermissionType.MENU, color: 'green' },
  { label: '按钮权限', value: PermissionType.BUTTON, color: 'orange' },
  { label: '数据权限', value: PermissionType.DATA, color: 'purple' }
]

/**
 * 获取权限类型描述
 */
export function getPermissionTypeDesc(permissionType: PermissionType): string {
  const option = PERMISSION_TYPE_OPTIONS.find(item => item.value === permissionType)
  return option?.label || '未知'
}

/**
 * 获取权限类型颜色
 */
export function getPermissionTypeColor(permissionType: PermissionType): string {
  const option = PERMISSION_TYPE_OPTIONS.find(item => item.value === permissionType)
  return option?.color || 'default'
}

/**
 * 权限状态枚举
 */
export enum PermissionStatus {
  /** 禁用 */
  DISABLED = 0,
  /** 启用 */
  ENABLED = 1
}

/**
 * 权限状态选项
 */
export const PERMISSION_STATUS_OPTIONS = [
  { label: '启用', value: PermissionStatus.ENABLED, color: 'success' },
  { label: '禁用', value: PermissionStatus.DISABLED, color: 'error' }
]

/**
 * 获取权限状态描述
 */
export function getPermissionStatusDesc(status: number): string {
  const option = PERMISSION_STATUS_OPTIONS.find(item => item.value === status)
  return option?.label || '未知'
}

/**
 * 获取权限状态颜色
 */
export function getPermissionStatusColor(status: number): string {
  const option = PERMISSION_STATUS_OPTIONS.find(item => item.value === status)
  return option?.color || 'default'
}

/**
 * 权限编码验证规则
 */
export const PERMISSION_CODE_PATTERN = /^[a-zA-Z0-9_-]+:[a-zA-Z0-9_-]+$/

/**
 * 验证权限编码格式
 */
export function validatePermissionCode(code: string): boolean {
  return PERMISSION_CODE_PATTERN.test(code)
}

/**
 * 从权限编码解析资源和动作
 */
export function parsePermissionCode(code: string): { resource: string; action: string } {
  if (!validatePermissionCode(code)) {
    return { resource: '', action: '' }
  }
  
  const [resource, action] = code.split(':')
  return { resource, action }
}

/**
 * 构建权限编码
 */
export function buildPermissionCode(resource: string, action: string): string {
  return `${resource}:${action}`
}

/**
 * 常用权限动作
 */
export const COMMON_ACTIONS = [
  { label: '查看', value: 'view' },
  { label: '列表', value: 'list' },
  { label: '创建', value: 'create' },
  { label: '编辑', value: 'edit' },
  { label: '删除', value: 'delete' },
  { label: '导出', value: 'export' },
  { label: '导入', value: 'import' },
  { label: '审核', value: 'audit' },
  { label: '启用', value: 'enable' },
  { label: '禁用', value: 'disable' }
]

/**
 * 常用资源类型
 */
export const COMMON_RESOURCES = [
  { label: '用户管理', value: 'user' },
  { label: '角色管理', value: 'role' },
  { label: '权限管理', value: 'permission' },
  { label: '菜单管理', value: 'menu' },
  { label: '字典管理', value: 'dictionary' },
  { label: '系统设置', value: 'system' },
  { label: '日志管理', value: 'log' },
  { label: '监控管理', value: 'monitor' }
]

/**
 * 权限树节点
 */
export interface PermissionTreeNode extends Permission {
  /** 节点标题 */
  title?: string
  /** 节点键值 */
  key?: string | number
  /** 是否选中 */
  checked?: boolean
  /** 是否展开 */
  expanded?: boolean
  /** 是否禁用 */
  disabled?: boolean
  /** 子节点 */
  children?: PermissionTreeNode[]
}

/**
 * 转换权限为树节点
 */
export function convertToTreeNode(permission: Permission): PermissionTreeNode {
  return {
    ...permission,
    title: permission.permissionName,
    key: permission.id,
    children: permission.children?.map(convertToTreeNode)
  }
}

/**
 * 转换权限列表为树节点列表
 */
export function convertToTreeNodes(permissions: Permission[]): PermissionTreeNode[] {
  return permissions.map(convertToTreeNode)
}

/**
 * 用户管理相关类型定义
 * 基于后端API文档生成
 */

import type { BaseEntity, PageResult, ApiResponse } from './common'

// 用户账户信息
export interface UserAccount extends BaseEntity {
  // 基本信息
  username: string
  email: string
  phoneNumber?: string
  passwordHash?: string
  passwordSalt?: string
  realName?: string
  nickname?: string
  avatarUrl?: string
  
  // 个人信息
  gender?: number // 性别：1-男，2-女，0-未知
  birthday?: string
  
  // 账户状态
  userType: number // 用户类型：1-管理员，2-租户管理员，3-普通用户
  accountStatus: number // 账户状态：1-正常，2-锁定，3-禁用，4-待激活
  isActivated: boolean
  emailVerified: boolean
  phoneVerified: boolean
  
  // 登录信息
  lastLoginTime?: string
  lastLoginIp?: string
  loginCount: number
  passwordErrorCount: number
  lockedTime?: string
  passwordExpireTime?: string
  accountExpireTime?: string
  
  // 组织信息
  departmentId?: number
  position?: string
  employeeNo?: string
  joinTime?: string
  
  // 扩展信息
  preferences?: string
  extraAttributes?: string
  
  // 计算属性
  normal: boolean
  locked: boolean
  banned: boolean
  pendingActivation: boolean
  admin: boolean
  superAdmin: boolean
  displayName: string
  businessKey: string
}

// 用户创建请求
export interface UserCreateRequest {
  username: string
  email: string
  password: string
  phoneNumber?: string
  realName?: string
  nickname?: string
  gender?: number
  userType: number
  departmentId?: number
  position?: string
  employeeNo?: string
  remark?: string
}

// 用户更新请求
export interface UserUpdateRequest {
  id: number
  email?: string
  phoneNumber?: string
  realName?: string
  nickname?: string
  avatarUrl?: string
  gender?: number
  birthday?: string
  userType?: number
  accountStatus?: number
  departmentId?: number
  position?: string
  employeeNo?: string
  joinTime?: string
  preferences?: string
  extraAttributes?: string
  remark?: string
  version: number
}

// 用户查询参数
export interface UserQueryParams {
  keyword?: string
  status?: number
  userType?: number
  departmentId?: number
  page?: number
  size?: number
}

// 用户列表响应
export type UserListResponse = ApiResponse<PageResult<UserAccount>>

// 用户详情响应
export type UserDetailResponse = ApiResponse<UserAccount>

// 用户操作响应
export type UserOperationResponse = ApiResponse<UserAccount>

// 用户状态枚举
export enum UserStatus {
  NORMAL = 1,
  LOCKED = 2,
  BANNED = 3,
  PENDING_ACTIVATION = 4
}

// 用户类型枚举
export enum UserType {
  ADMIN = 1,
  TENANT_ADMIN = 2,
  NORMAL_USER = 3
}

// 性别枚举
export enum Gender {
  UNKNOWN = 0,
  MALE = 1,
  FEMALE = 2
}

// 用户状态选项
export const USER_STATUS_OPTIONS = [
  { label: '正常', value: UserStatus.NORMAL, color: 'green' },
  { label: '锁定', value: UserStatus.LOCKED, color: 'orange' },
  { label: '禁用', value: UserStatus.BANNED, color: 'red' },
  { label: '待激活', value: UserStatus.PENDING_ACTIVATION, color: 'blue' }
]

// 用户类型选项
export const USER_TYPE_OPTIONS = [
  { label: '管理员', value: UserType.ADMIN, color: 'purple' },
  { label: '租户管理员', value: UserType.TENANT_ADMIN, color: 'blue' },
  { label: '普通用户', value: UserType.NORMAL_USER, color: 'default' }
]

// 性别选项
export const GENDER_OPTIONS = [
  { label: '未知', value: Gender.UNKNOWN },
  { label: '男', value: Gender.MALE },
  { label: '女', value: Gender.FEMALE }
]

// 用户表格列配置
export interface UserTableColumn {
  key: string
  title: string
  dataIndex: string
  width?: number
  fixed?: 'left' | 'right'
  sorter?: boolean
  filters?: Array<{ text: string; value: any }>
}

// 用户操作权限
export interface UserPermissions {
  canCreate: boolean
  canUpdate: boolean
  canDelete: boolean
  canView: boolean
  canResetPassword: boolean
  canChangeStatus: boolean
  canAssignRole: boolean
}

// 用户角色分配请求
export interface UserRoleAssignRequest {
  userId: number
  roleIds: number[]
  operation: 'assign' | 'revoke'
  remark?: string
}

// 用户密码重置请求
export interface UserPasswordResetRequest {
  userId: number
  newPassword: string
  confirmPassword: string
  forceChange?: boolean
}

// 用户批量操作请求
export interface UserBatchOperationRequest {
  userIds: number[]
  operation: 'enable' | 'disable' | 'lock' | 'unlock' | 'delete'
  remark?: string
}

// 用户导入数据
export interface UserImportData {
  username: string
  email: string
  realName?: string
  phoneNumber?: string
  departmentName?: string
  position?: string
  employeeNo?: string
}

// 用户导出参数
export interface UserExportParams {
  keyword?: string
  status?: number
  userType?: number
  departmentId?: number
  format: 'excel' | 'csv'
  fields: string[]
}

// 用户统计信息
export interface UserStatistics {
  totalUsers: number
  activeUsers: number
  lockedUsers: number
  bannedUsers: number
  pendingUsers: number
  newUsersToday: number
  newUsersThisWeek: number
  newUsersThisMonth: number
  loginUsersToday: number
  loginUsersThisWeek: number
  loginUsersThisMonth: number
}

// 用户活动日志
export interface UserActivityLog {
  id: number
  userId: number
  username: string
  action: string
  description: string
  ipAddress: string
  userAgent: string
  createTime: string
  status: 'success' | 'failed'
}

// 用户在线状态
export interface UserOnlineStatus {
  userId: number
  username: string
  realName?: string
  lastActiveTime: string
  ipAddress: string
  deviceInfo: string
  isOnline: boolean
}

import { createRouter, createWebHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'

// 导入布局组件
const BasicLayout = () => import('@/layouts/BasicLayout.vue')
const BlankLayout = () => import('@/layouts/BlankLayout.vue')

// 路由配置
const routes: RouteRecordRaw[] = [
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/auth/Login.vue'),
    meta: {
      title: '登录',
      hideInMenu: true,
      requiresAuth: false
    }
  },
  {
    path: '/test/auth',
    name: 'AuthTest',
    component: () => import('@/views/test/AuthTest.vue'),
    meta: {
      title: 'JWT认证测试',
      hideInMenu: true,
      requiresAuth: false
    }
  },
  {
    path: '/debug/login',
    name: 'LoginDebug',
    component: () => import('@/views/debug/LoginTest.vue'),
    meta: {
      title: '登录调试',
      hideInMenu: true,
      requiresAuth: false
    }
  },
  {
    path: '/',
    name: 'Root',
    component: BasicLayout,
    redirect: '/dashboard',
    meta: {
      title: '首页',
      icon: 'home',
      requiresAuth: true
    },
    children: [
      {
        path: '/dashboard',
        name: 'Dashboard',
        component: () => import('@/views/dashboard/index.vue'),
        meta: {
          title: '仪表盘',
          icon: 'dashboard',
          requiresAuth: true
        }
      }
    ]
  },
  {
    path: '/member',
    name: 'Member',
    component: BasicLayout,
    redirect: '/member/user',
    meta: {
      title: '用户管理',
      icon: 'user',
      requiresAuth: true,
      permissions: ['user:view']
    },
    children: [
      {
        path: '/member/user',
        name: 'MemberUser',
        component: () => import('@/views/member/user/index.vue'),
        meta: {
          title: '用户列表',
          requiresAuth: true,
          permissions: ['user:list']
        }
      },
      {
        path: '/member/role',
        name: 'MemberRole',
        component: () => import('@/views/member/role/index.vue'),
        meta: {
          title: '角色管理',
          requiresAuth: true,
          permissions: ['role:list']
        }
      },
      {
        path: '/member/permission',
        name: 'MemberPermission',
        component: () => import('@/views/member/permission/index.vue'),
        meta: {
          title: '权限管理',
          requiresAuth: true,
          permissions: ['permission:list']
        }
      }
    ]
  },
  {
    path: '/product',
    name: 'Product',
    component: BasicLayout,
    redirect: '/product/list',
    meta: {
      title: '商品管理',
      icon: 'shopping',
      requiresAuth: true
    },
    children: [
      {
        path: '/product/list',
        name: 'ProductList',
        component: () => import('@/views/product/list/index.vue'),
        meta: {
          title: '商品列表',
          requiresAuth: true
        }
      },
      {
        path: '/product/category',
        name: 'ProductCategory',
        component: () => import('@/views/product/category/index.vue'),
        meta: {
          title: '商品分类',
          requiresAuth: true
        }
      },
      {
        path: '/product/brand',
        name: 'ProductBrand',
        component: () => import('@/views/product/brand/index.vue'),
        meta: {
          title: '品牌管理',
          requiresAuth: true
        }
      }
    ]
  },
  {
    path: '/inventory',
    name: 'Inventory',
    component: BasicLayout,
    redirect: '/inventory/stock',
    meta: {
      title: '库存管理',
      icon: 'database',
      requiresAuth: true
    },
    children: [
      {
        path: '/inventory/stock',
        name: 'InventoryStock',
        component: () => import('@/views/inventory/stock/index.vue'),
        meta: {
          title: '库存查询',
          requiresAuth: true
        }
      },
      {
        path: '/inventory/alert',
        name: 'InventoryAlert',
        component: () => import('@/views/inventory/alert/index.vue'),
        meta: {
          title: '库存预警',
          requiresAuth: true
        }
      },
      {
        path: '/inventory/log',
        name: 'InventoryLog',
        component: () => import('@/views/inventory/log/index.vue'),
        meta: {
          title: '库存日志',
          requiresAuth: true
        }
      }
    ]
  },
  {
    path: '/order',
    name: 'Order',
    component: BasicLayout,
    redirect: '/order/list',
    meta: {
      title: '订单管理',
      icon: 'file-text',
      requiresAuth: true
    },
    children: [
      {
        path: '/order/list',
        name: 'OrderList',
        component: () => import('@/views/order/list/index.vue'),
        meta: {
          title: '订单列表',
          requiresAuth: true
        }
      },
      {
        path: '/order/detail/:id',
        name: 'OrderDetail',
        component: () => import('@/views/order/detail/index.vue'),
        meta: {
          title: '订单详情',
          hideInMenu: true,
          requiresAuth: true
        }
      }
    ]
  },
  {
    path: '/platform',
    name: 'Platform',
    component: BasicLayout,
    redirect: '/platform/integration',
    meta: {
      title: '平台集成',
      icon: 'api',
      requiresAuth: true
    },
    children: [
      {
        path: '/platform/integration',
        name: 'PlatformIntegration',
        component: () => import('@/views/platform/integration/index.vue'),
        meta: {
          title: '平台配置',
          requiresAuth: true
        }
      },
      {
        path: '/platform/sync',
        name: 'PlatformSync',
        component: () => import('@/views/platform/sync/index.vue'),
        meta: {
          title: '数据同步',
          requiresAuth: true
        }
      }
    ]
  },
  {
    path: '/system',
    name: 'System',
    component: BasicLayout,
    redirect: '/system/tenant',
    meta: {
      title: '系统管理',
      icon: 'setting',
      requiresAuth: true
    },
    children: [
      {
        path: '/system/user',
        name: 'SystemUser',
        component: () => import('@/views/system/user/index.vue'),
        meta: {
          title: '用户管理',
          requiresAuth: true,
          permissions: ['user:list']
        }
      },
      {
        path: '/system/role',
        name: 'SystemRole',
        component: () => import('@/views/system/role/index.vue'),
        meta: {
          title: '角色管理',
          requiresAuth: true,
          permissions: ['role:list']
        }
      },
      {
        path: '/system/permission',
        name: 'SystemPermission',
        component: () => import('@/views/system/permission/index.vue'),
        meta: {
          title: '权限管理',
          requiresAuth: true,
          permissions: ['permission:list']
        }
      },
      {
        path: '/system/menu',
        name: 'SystemMenu',
        component: () => import('@/views/system/menu/index.vue'),
        meta: {
          title: '菜单管理',
          requiresAuth: true,
          permissions: ['menu:list']
        }
      },
      {
        path: '/system/tenant',
        name: 'SystemTenant',
        component: () => import('@/views/system/tenant/index.vue'),
        meta: {
          title: '租户管理',
          requiresAuth: true
        }
      },
      {
        path: '/system/config',
        name: 'SystemConfig',
        component: () => import('@/views/system/config/index.vue'),
        meta: {
          title: '系统配置',
          requiresAuth: true
        }
      }
    ]
  },
  {
    path: '/403',
    name: 'Forbidden',
    component: () => import('@/views/system/403/index.vue'),
    meta: {
      title: '访问被拒绝',
      hideInMenu: true,
      requiresAuth: false
    }
  },
  {
    path: '/404',
    name: 'NotFound',
    component: () => import('@/views/system/404/index.vue'),
    meta: {
      title: '页面不存在',
      hideInMenu: true,
      requiresAuth: false
    }
  },
  {
    path: '/:pathMatch(.*)*',
    redirect: '/404'
  }
]

// 创建路由实例
const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else {
      return { top: 0 }
    }
  }
})

export default router

# 应用基础配置
VITE_APP_TITLE=VisThink ERP 管理系统
VITE_APP_SHORT_NAME=VisThink ERP
VITE_APP_DESCRIPTION=基于 Vue Vben Admin 的多租户电商 ERP 系统

# API 基础地址
VITE_API_BASE_URL=http://localhost:8080

# 微服务地址配置
VITE_MEMBER_API_URL=http://localhost:8081
VITE_PRODUCT_API_URL=http://localhost:8082
VITE_INVENTORY_API_URL=http://localhost:8083
VITE_ORDER_API_URL=http://localhost:8084
VITE_PLATFORM_API_URL=http://localhost:8085

# 应用配置
VITE_APP_LOCALE=zh-CN
VITE_APP_THEME=light
VITE_APP_LAYOUT=sidebar

# 开发配置
VITE_DEV_TOOLS=true
VITE_MOCK_ENABLED=true

# 构建配置
VITE_BUILD_COMPRESS=gzip
VITE_BUILD_ANALYZE=false

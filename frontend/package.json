{"name": "visthink-erp-frontend", "version": "1.0.0", "description": "VisThink ERP 前端项目 - 基于 Vue 3 + Ant Design Vue", "type": "module", "scripts": {"dev": "vite --host 0.0.0.0 --port 3000", "build": "vue-tsc && vite build", "build:analyze": "vite build --mode analyze", "build:docker": "vite build --mode docker", "preview": "vite preview --host 0.0.0.0 --port 4173", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "lint:stylelint": "stylelint **/*.{vue,css,scss,postcss,less} --fix", "type-check": "vue-tsc --noEmit", "clean": "rimraf dist node_modules/.vite", "reinstall": "rimraf pnpm-lock.yaml && rimraf package-lock.json && rimraf yarn.lock && rimraf node_modules && npm install"}, "dependencies": {"@ant-design/icons-vue": "^7.0.1", "@element-plus/icons-vue": "^2.3.1", "@vueuse/core": "^10.11.0", "ant-design-vue": "^4.2.3", "axios": "^1.7.2", "bpmn-js": "^17.2.1", "bpmn-js-properties-panel": "^5.10.0", "bpmn-js-color-picker": "^0.7.0", "diagram-js-minimap": "^4.0.1", "dayjs": "^1.11.11", "element-plus": "^2.10.2", "lodash-es": "^4.17.21", "pinia": "^2.1.7", "sass-embedded": "^1.89.2", "vue": "^3.4.31", "vue-i18n": "^9.13.1", "vue-router": "^4.4.0"}, "devDependencies": {"@rushstack/eslint-patch": "^1.10.3", "@types/lodash-es": "^4.17.12", "@types/node": "^20.14.10", "@typescript-eslint/eslint-plugin": "^7.16.0", "@typescript-eslint/parser": "^7.16.0", "@vitejs/plugin-vue": "^5.0.5", "@vitejs/plugin-vue-jsx": "^4.0.0", "@vue/eslint-config-prettier": "^9.0.0", "@vue/eslint-config-typescript": "^13.0.0", "@vue/tsconfig": "^0.5.1", "autoprefixer": "^10.4.19", "eslint": "^8.57.0", "eslint-plugin-vue": "^9.27.0", "less": "^4.2.0", "postcss": "^8.4.39", "prettier": "^3.3.2", "rimraf": "^5.0.7", "stylelint": "^16.6.1", "stylelint-config-recess-order": "^5.0.1", "stylelint-config-standard": "^36.0.1", "stylelint-config-standard-vue": "^1.0.0", "typescript": "^5.5.3", "unplugin-auto-import": "^0.17.6", "unplugin-vue-components": "^0.27.2", "vite": "^5.3.3", "vue-tsc": "^2.0.24"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}